import React from 'react';
import type { CornerInfoConfig } from './LeftToolBar/CornerInfoModal';
import { processSensitiveText, isSensitiveField } from '@/utils/sensitiveInfo';

interface CornerInfoOverlayProps {
  config: CornerInfoConfig;
  imageData?: any; // DICOM 标签数据
  imageType: 'ct' | 'pet' | 'fusion';
  style?: React.CSSProperties;
  sensitiveInfoHidden?: boolean; // 是否隐藏敏感信息
  mousePosition?: { x: number; y: number }; // 鼠标位置
  pixelValue?: string; // 像素值
  showProbe?: boolean; // 是否显示点测量信息
  windowWidth?: number; // 窗宽
  windowCenter?: number; // 窗位
  index?: number;
}

interface InfoItem {
  label: string;
  value: string | number | undefined;
}

const CornerInfoOverlay: React.FC<CornerInfoOverlayProps> = ({
  config,
  imageData,
  imageType,
  style,
  sensitiveInfoHidden = false,
  mousePosition,
  pixelValue,
  showProbe,
  windowWidth,
  windowCenter,
  index = 0,
}) => {
  // console.log('=== CornerInfoOverlay 组件渲染 ===');
  // console.log('传入的config:', config);
  // console.log('传入的imageData:', imageData);
  // console.log('传入的imageType:', imageType);
  // console.log('config.enabled:', config?.enabled);

  // 如果配置为隐藏，则不渲染
  if (!config.enabled) {
    console.log('四角信息被禁用，不显示');
    return null;
  }

  // console.log('CornerInfoOverlay 渲染参数:');
  // console.log('config:', config);
  // console.log('imageData:', imageData);
  // console.log('imageType:', imageType);

  // 监听config变化
  React.useEffect(() => {
    console.log('CornerInfoOverlay config更新:', config);
    console.log('CornerInfoOverlay imageData更新:', imageData);
  }, [config]);

  // 格式化日期
  const formatDate = (dateStr?: string) => {
    if (!dateStr) return '';
    return dateStr.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3');
  };

  // 格式化时间
  const formatTime = (timeStr?: string) => {
    if (!timeStr) return '';
    return timeStr.replace(/(\d{2})(\d{2})(\d{2})/, '$1:$2:$3');
  };

  // console.log('处理DICOM标签数据:', imageData);

  // 按照配置分类将信息项分配到对应的角落
  const getCornerItems = () => {
    const topLeft: InfoItem[] = [];
    const topRight: InfoItem[] = [];
    const bottomLeft: InfoItem[] = [];
    const bottomRight: InfoItem[] = [];

    

    if (!imageData) return { topLeft, topRight, bottomLeft, bottomRight };

    const getDicomValue = (tag: string): string => {
      const tagData = imageData[tag];
      if (tagData && tagData.Value && Array.isArray(tagData.Value) && tagData.Value.length > 0) {
        return String(tagData.Value[0]);
      }
      return '';
    };

    // 格式化年龄，将"53Y"转换为"53"
    const formatAge = (ageValue: string): string => {
      if (!ageValue) return '';
      // 移除末尾的Y、M、D等单位字符
      return ageValue.replace(/[YMD]$/i, '');
    };

    // 格式化性别，将"M"/"F"转换为"男"/"女"
    const formatSex = (sexValue: string): string => {
      if (!sexValue) return '';
      const sexMap: Record<string, string> = {
        M: '男',
        F: '女',
        Male: '男',
        Female: '女',
        MALE: '男',
        FEMALE: '女',
        male: '男',
        female: '女',
      };
      return sexMap[sexValue] || sexValue;
    };

    // 处理敏感信息的辅助函数
    const addInfoItem = (items: InfoItem[], label: string, value: string) => {
      if (!value) return;

      const processedValue = isSensitiveField(label) ? processSensitiveText(value, sensitiveInfoHidden) : value;

      items.push({ label, value: processedValue });
    };

    if (imageType === 'ct') {
      const ctConfig = config.ct;

      // 左上：患者信息
      if (ctConfig.patientName) {
        const value = getDicomValue('(0010,0010)');
        addInfoItem(topLeft, '患者姓名', value);
      }
      if (ctConfig.patientId) {
        const value = getDicomValue('(0010,0020)');
        addInfoItem(topLeft, '患者编号', value);
      }
      if (ctConfig.patientAge) {
        const value = getDicomValue('(0010,1010)');
        const formattedAge = formatAge(value);
        addInfoItem(topLeft, '患者年龄', formattedAge);
      }
      if (ctConfig.patientSex) {
        const value = getDicomValue('(0010,0040)');
        const formattedSex = formatSex(value);
        addInfoItem(topLeft, '患者性别', formattedSex);
      }
      if (ctConfig.studyDescription) {
        const value = getDicomValue('(0008,1030)');
        if (value) topLeft.push({ label: '检查部位', value });
      }
      if (ctConfig.studyDate) {
        const value = getDicomValue('(0008,0020)');
        if (value) topLeft.push({ label: '检查日期', value: formatDate(value) });
      }
      if (ctConfig.studyTime) {
        const value = getDicomValue('(0008,0030)');
        if (value) topLeft.push({ label: '检查时间', value: formatTime(value) });
      }
      if (ctConfig.seriesNumber) {
        const value = index + 1;
        if (value) topLeft.push({ label: '序列号', value });
      }
      if (ctConfig.patientPosition) {
        const value = getDicomValue('(0018,5100)');
        if (value) topLeft.push({ label: '患者摆位', value });
      }
      if (ctConfig.patientBirthDate) {
        const value = getDicomValue('(0010,0030)');
        if (value) topLeft.push({ label: '患者出生日期', value: formatDate(value) });
      }

      // 右上：设备信息 - 机构名称在右上第一个
      if (ctConfig.institutionName) {
        const value = getDicomValue('(0008,0080)');
        if (value) topRight.push({ label: '机构名称', value });
      }
      if (ctConfig.manufacturer) {
        const value = getDicomValue('(0008,0070)');
        if (value) topRight.push({ label: '制造商', value });
      }
      if (ctConfig.manufacturerModelName) {
        const value = getDicomValue('(0008,1090)');
        if (value) topRight.push({ label: '设备型号', value });
      }
      if (ctConfig.modality) {
        const value = getDicomValue('(0008,0060)');
        if (value) topRight.push({ label: '模态', value });
      }
      if (ctConfig.softwareVersions) {
        const value = getDicomValue('(0018,1020)');
        if (value) topRight.push({ label: '软件版本', value });
      }
      if (ctConfig.protocolName) {
        const value = getDicomValue('(0018,1030)');
        if (value) topRight.push({ label: '协议名称', value });
      }
      if (ctConfig.accessionNumber) {
        const value = getDicomValue('(0008,0050)');
        if (value) topRight.push({ label: '登记号', value });
      }

      // 左下：技术参数 - 序列描述在左下第一个
      if (ctConfig.seriesDescription) {
        const value = getDicomValue('(0008,103e)');
        if (value) bottomLeft.push({ label: '序列描述', value });
      }
      if (ctConfig.kvp) {
        const value = getDicomValue('(0018,0060)');
        if (value) bottomLeft.push({ label: '管电压', value: `${value}kV` });
      }
      if (ctConfig.xRayTubeCurrent) {
        const value = getDicomValue('(0018,1151)');
        if (value) bottomLeft.push({ label: '管电流', value: `${value}mA` });
      }
      if (ctConfig.sliceThickness) {
        const value = getDicomValue('(0018,0050)');
        if (value) bottomLeft.push({ label: '层厚', value: `${value}mm` });
      }
      if (ctConfig.tableHeight) {
        const value = getDicomValue('(0018,1130)');
        if (value) bottomLeft.push({ label: '床高', value: `${value}mm` });
      }
      if (ctConfig.spiralPitchFactor) {
        const value = getDicomValue('(0018,9311)');
        if (value) bottomLeft.push({ label: '螺距', value });
      }
      if (ctConfig.exposureTime) {
        const value = getDicomValue('(0018,1150)');
        if (value) bottomLeft.push({ label: '曝光时间', value: `${value}ms` });
      }
      if (ctConfig.reconstructionDiameter) {
        const value = getDicomValue('(0018,1100)');
        if (value) bottomLeft.push({ label: '重建尺寸', value: `${value}mm` });
      }
      if (ctConfig.convolutionKernel) {
        const value = getDicomValue('(0018,1210)');
        if (value) bottomLeft.push({ label: '重建卷积核', value });
      }

      // 右下：显示参数 - 缩放比例在右下第一个
      if (ctConfig.imageZoomFactor) {
        bottomRight.push({ label: '缩放比例', value: '100%' });
      }
      if (ctConfig.windowWidth) {
        // const windowWidth = getDicomValue('(0028,1051)');
        // const windowCenter = getDicomValue('(0028,1050)');
        const width = windowWidth || getDicomValue('(0028,1051)');
        const center = windowCenter || getDicomValue('(0028,1050)');
        if (width || center) {
          const windowValue = `${center || '0'}/${width || '0'}`;
          bottomRight.push({ label: '窗宽窗位', value: windowValue });
        }
      }
    } else if (imageType === 'pet') {
      const petConfig = config.pet;
      console.log('petConfig', petConfig);

      // 左上：患者信息
      if (petConfig.patientName) {
        const value = getDicomValue('(0010,0010)');
        addInfoItem(topLeft, '患者姓名', value);
      }
      if (petConfig.patientId) {
        const value = getDicomValue('(0010,0020)');
        addInfoItem(topLeft, '患者编号', value);
      }
      if (petConfig.patientAge) {
        const value = getDicomValue('(0010,1010)');
        const formattedAge = formatAge(value);
        addInfoItem(topLeft, '患者年龄', formattedAge);
      }
      if (petConfig.patientSex) {
        const value = getDicomValue('(0010,0040)');
        const formattedSex = formatSex(value);
        addInfoItem(topLeft, '患者性别', formattedSex);
      }
      if (petConfig.studyDescription) {
        const value = getDicomValue('(0008,1030)');
        if (value) topLeft.push({ label: '检查部位', value });
      }
      if (petConfig.studyDate) {
        const value = getDicomValue('(0008,0020)');
        if (value) topLeft.push({ label: '检查日期', value: formatDate(value) });
      }
      if (petConfig.studyTime) {
        const value = getDicomValue('(0008,0030)');
        if (value) topLeft.push({ label: '检查时间', value: formatTime(value) });
      }
      if (petConfig.seriesNumber) {
        const value = index + 1;
        if (value) topLeft.push({ label: '序列号', value });
      }
      if (petConfig.patientPosition) {
        const value = getDicomValue('(0018,5100)');
        if (value) topLeft.push({ label: '患者摆位', value });
      }
      if (petConfig.patientBirthDate) {
        const value = getDicomValue('(0010,0030)');
        if (value) topLeft.push({ label: '患者出生日期', value: formatDate(value) });
      }

      // 右上：设备信息 - 机构名称在右上第一个
      if (petConfig.institutionName) {
        const value = getDicomValue('(0008,0080)');
        if (value) topRight.push({ label: '机构名称', value });
      }
      if (petConfig.manufacturer) {
        const value = getDicomValue('(0008,0070)');
        if (value) topRight.push({ label: '制造商', value });
      }
      if (petConfig.manufacturerModelName) {
        const value = getDicomValue('(0008,1090)');
        if (value) topRight.push({ label: '设备型号', value });
      }
      if (petConfig.modality) {
        const value = getDicomValue('(0008,0060)');
        if (value) topRight.push({ label: '模态', value });
      }
      if (petConfig.softwareVersions) {
        const value = getDicomValue('(0018,1020)');
        if (value) topRight.push({ label: '软件版本', value });
      }
      if (petConfig.protocolName) {
        const value = getDicomValue('(0018,1030)');
        if (value) topRight.push({ label: '协议名称', value });
      }
      if (petConfig.accessionNumber) {
        const value = getDicomValue('(0008,0050)');
        if (value) topRight.push({ label: '登记号', value });
      }

      // 左下：PET特有参数 - 同位素名称在左下第一个
      if (petConfig.radiopharmaceutical) {
        const value = getDicomValue('(0018,0031)');
        if (value) bottomLeft.push({ label: '同位素名称', value });
      }
      if (petConfig.seriesDescription) {
        const value = getDicomValue('(0008,103e)');
        if (value) bottomLeft.push({ label: '序列描述', value });
      }
      if (petConfig.matrixSize) {
        const rows = getDicomValue('(0028,0010)');
        const cols = getDicomValue('(0028,0011)');
        if (rows && cols) bottomLeft.push({ label: '行列/像素矩阵', value: `${rows}×${cols}` });
      }
      if (petConfig.sliceThickness) {
        const value = getDicomValue('(0018,0050)');
        if (value) bottomLeft.push({ label: '层厚', value: `${value}mm` });
      }
      if (petConfig.reconstructionDiameter) {
        const value = getDicomValue('(0018,1100)');
        if (value) bottomLeft.push({ label: '重建视野', value: `${value}mm` });
      }
      if (petConfig.numberOfSlices) {
        const value = getDicomValue('(0054,0081)');
        if (value) bottomLeft.push({ label: '图像层数', value });
      }
      if (petConfig.currentSlicePosition) {
        const value = getDicomValue('(0020,0013)');
        if (value) bottomLeft.push({ label: '当前切换位置', value: `第${value}层` });
      }

      // 右下：显示参数 - 缩放比例在右下第一个
      if (petConfig.imageZoomFactor) {
        bottomRight.push({ label: '缩放比例', value: '100%' });
      }
      if (petConfig.suvThreshold) {
        // SUV阈值显示逻辑
        bottomRight.push({ label: 'SUV阈值', value: '2.5' });
      }
    } else if (imageType === 'fusion') {
      const fusionConfig = config.fusion;

      // 左上：患者信息
      if (fusionConfig.patientName) {
        const value = getDicomValue('(0010,0010)');
        addInfoItem(topLeft, '患者姓名', value);
      }
      if (fusionConfig.patientId) {
        const value = getDicomValue('(0010,0020)');
        addInfoItem(topLeft, '患者编号', value);
      }
      if (fusionConfig.patientAge) {
        const value = getDicomValue('(0010,1010)');
        const formattedAge = formatAge(value);
        addInfoItem(topLeft, '患者年龄', formattedAge);
      }
      if (fusionConfig.patientSex) {
        const value = getDicomValue('(0010,0040)');
        const formattedSex = formatSex(value);
        addInfoItem(topLeft, '患者性别', formattedSex);
      }
      if (fusionConfig.studyDescription) {
        const value = getDicomValue('(0008,1030)');
        if (value) topLeft.push({ label: '检查部位', value });
      }
      if (fusionConfig.studyDate) {
        const value = getDicomValue('(0008,0020)');
        if (value) topLeft.push({ label: '检查日期', value: formatDate(value) });
      }
      if (fusionConfig.studyTime) {
        const value = getDicomValue('(0008,0030)');
        if (value) topLeft.push({ label: '检查时间', value: formatTime(value) });
      }
      if (fusionConfig.seriesNumber) {
        const value = index + 1;
        if (value) topLeft.push({ label: '序列号', value });
      }
      if (fusionConfig.patientPosition) {
        const value = getDicomValue('(0018,5100)');
        if (value) topLeft.push({ label: '患者摆位', value });
      }
      if (fusionConfig.patientBirthDate) {
        const value = getDicomValue('(0010,0030)');
        if (value) topLeft.push({ label: '患者出生日期', value: formatDate(value) });
      }

      // 右上：设备信息 - 机构名称在右上第一个
      if (fusionConfig.institutionName) {
        const value = getDicomValue('(0008,0080)');
        if (value) topRight.push({ label: '机构名称', value });
      }
      if (fusionConfig.manufacturer) {
        const value = getDicomValue('(0008,0070)');
        if (value) topRight.push({ label: '制造商', value });
      }
      if (fusionConfig.manufacturerModelName) {
        const value = getDicomValue('(0008,1090)');
        if (value) topRight.push({ label: '设备型号', value });
      }
      if (fusionConfig.modality) {
        const value = getDicomValue('(0008,0060)');
        if (value) topRight.push({ label: '模态', value });
      }
      if (fusionConfig.softwareVersions) {
        const value = getDicomValue('(0018,1020)');
        if (value) topRight.push({ label: '软件版本', value });
      }
      if (fusionConfig.protocolName) {
        const value = getDicomValue('(0018,1030)');
        if (value) topRight.push({ label: '协议名称', value });
      }
      if (fusionConfig.accessionNumber) {
        const value = getDicomValue('(0008,0050)');
        if (value) topRight.push({ label: '登记号', value });
      }

      // 左下：无内容

      // 右下：显示参数 - 融合四角信息设置右下不应该有鼠标位置和鼠标位置的值
      if (fusionConfig.imageZoomFactor) {
        bottomRight.push({ label: '缩放比例', value: '100%' });
      }
    }

    // 鼠标位置和像素值信息根据配置显示
    if (imageType === 'ct' && config.ct.cursorPosition && mousePosition) {
      bottomRight.push({ label: '鼠标位置', value: `X: ${mousePosition.x.toFixed(0)}, Y: ${mousePosition.y.toFixed(0)}` });
    }
    if (imageType === 'ct' && config.ct.pixelValue && pixelValue) {
      bottomRight.push({ label: '像素值', value: pixelValue });
    }
    if (imageType === 'pet' && config.pet.cursorPosition && mousePosition) {
      bottomRight.push({ label: '鼠标位置', value: `X: ${mousePosition.x.toFixed(0)}, Y: ${mousePosition.y.toFixed(0)}` });
    }
    if (imageType === 'pet' && config.pet.pixelValue && pixelValue) {
      bottomRight.push({ label: '像素值', value: pixelValue });
    }
    if (imageType === 'fusion' && config.fusion.cursorPosition && mousePosition) {
      bottomRight.push({ label: '鼠标位置', value: `X: ${mousePosition.x.toFixed(0)}, Y: ${mousePosition.y.toFixed(0)}` });
    }
    if (imageType === 'fusion' && config.fusion.pixelValue && pixelValue) {
      bottomRight.push({ label: '像素值', value: pixelValue });
    }

    return { topLeft, topRight, bottomLeft, bottomRight };
  };

  const { topLeft, topRight, bottomLeft, bottomRight } = getCornerItems();

  // console.log('四角分配:', { topLeft, topRight, bottomLeft, bottomRight });

  const renderCornerInfo = (items: InfoItem[], position: string) => {
    if (items.length === 0) return null;

    // console.log(`渲染${position}角落信息:`, items);

    const baseStyle: React.CSSProperties = {
      position: 'absolute',
      padding: '4px 8px',
      backgroundColor: 'transparent',
      color: 'white',
      fontSize: '12px',
      lineHeight: '1.2',
      borderRadius: '2px',
      maxWidth: '200px',
      wordWrap: 'break-word',
      zIndex: 10,
    };

    let positionStyle: React.CSSProperties = {};
    switch (position) {
      case '左上':
        positionStyle = { top: 40, left: 4 }; // 向下调整，避免与ViewControls重叠
        break;
      case '右上':
        positionStyle = { top: 40, right: 4 }; // 向下调整，避免与ViewControls重叠
        break;
      case '左下':
        positionStyle = { bottom: 4, left: 4 };
        break;
      case '右下':
        positionStyle = { bottom: 4, right: 4 };
        break;
    }

    return (
      <div style={{ ...baseStyle, ...positionStyle }}>
        {items.map((item, index) => (
          <div key={index} style={{ marginBottom: index < items.length - 1 ? '2px' : 0 }}>
            {item.value}
          </div>
        ))}
      </div>
    );
  };

  const overlayStyle: React.CSSProperties = {
    position: 'absolute',
    top: 8,
    left: 8,
    right: 8,
    bottom: 8,
    pointerEvents: 'none',
    zIndex: 10,
    ...style,
  };

  // console.log('最终渲染四角信息');

  return (
    <div style={overlayStyle}>
      {renderCornerInfo(topLeft, '左上')}
      {renderCornerInfo(topRight, '右上')}
      {renderCornerInfo(bottomLeft, '左下')}
      {renderCornerInfo(bottomRight, '右下')}
    </div>
  );
};

export default CornerInfoOverlay;
