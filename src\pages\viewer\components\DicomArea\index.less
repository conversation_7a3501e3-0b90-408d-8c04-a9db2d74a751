// .cineSelect {
//   :global {
//     .ant-select-selector {
//       background-color: #000 !important;
//       color: #fff !important;
//     }

//     .ant-select-item {
//       color: #fff !important;
//       background-color: #000 !important;
//     }

//     .ant-select-item:hover {
//       background-color: #222 !important;
//     }
//   }
// }

.seriesControls {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 10;
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  padding: 5px 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

  .controlItem {
    display: flex;
    align-items: center;
    margin-right: 12px;

    &:last-child {
      margin-right: 0;
    }
  }

  .controlLabel {
    color: #fff;
    margin-right: 6px;
    font-size: 12px;
  }
}

.viewControls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  padding: 5px 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

  .controlItem {
    display: flex;
    align-items: center;
    margin-right: 12px;

    &:last-child {
      margin-right: 0;
    }
  }

  .controlLabel {
    color: #fff;
    margin-right: 6px;
    font-size: 12px;
  }

  :global {
    .ant-select {
      .ant-select-selector {
        background-color: rgba(30, 30, 30, 0.8) !important;
        border-color: #444 !important;
        color: #fff !important;
      }

      .ant-select-arrow {
        color: #aaa !important;
      }
    }

    .ant-select-dropdown {
      background-color: rgba(30, 30, 30, 0.95) !important;

      .ant-select-item {
        color: #ddd !important;
      }

      .ant-select-item-option-selected {
        background-color: rgba(0, 123, 255, 0.3) !important;
      }

      .ant-select-item-option-active {
        background-color: rgba(0, 123, 255, 0.2) !important;
      }
    }
  }
}