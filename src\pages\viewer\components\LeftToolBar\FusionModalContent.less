.fusionModalContent {
  width: 1200px;
  height: 700px;

  .ctModalWrapper {
    border: none;
    border-radius: 4px;
    padding: 12px;
    background-color: #2a2a2a;
  }

  .petModalWrapper {
    border: none;
    border-radius: 4px;
    padding: 12px;
    background-color: #2a2a2a;
  }

  .seriesListHeader {
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #444;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #fff;
    }
  }

  .seriesListContainer {
    height: 580px;
    overflow-y: auto;
    padding: 0;
    border: none;
    border-radius: 4px;
    background-color: #262626;

    :global {
      .ant-radio-wrapper {
        display: block;
        margin: 0;
        padding: 6px 8px;
        border-radius: 0;
        transition: all 0.3s;
        color: #fff;

        &:hover {
          background-color: #444;
        }

        .ant-radio-inner {
          background-color: transparent;
          border-color: #666;
        }

        .ant-radio-checked .ant-radio-inner {
          border-color: #1890ff;
          &::after {
            background-color: #1890ff;
          }
        }
      }

      .ant-empty-description {
        color: #999;
      }
    }
  }

  .matchedSeries {
    :global {
      .ant-radio-wrapper {
        background-color: rgba(24, 144, 255, 0.15);
        border: 1px solid rgba(24, 144, 255, 0.5);

        &:hover {
          background-color: rgba(24, 144, 255, 0.25);
        }
      }
    }
  }

  .disabledText {
    color: #666;
  }

  .modalFooter {
    margin-top: 24px;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }

  // 表格样式
  .tableHeader {
    display: flex;
    background-color: #333233;
    border-bottom: 1px solid #444;
    padding: 8px 0;

    .radioCell {
      width: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .sequenceCell {
      width: 80px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #fff;
    }

    .descriptionCell {
      flex: 1;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      color: #fff;
      padding-left: 20px;
    }
  }

  .tableRow {
    display: flex;
    border-bottom: 1px solid #444;
    background-color: #262626;

    &.selected {
      // 选中时只改变字体颜色，不改变背景色
    }

    &.matched {
      background-color: rgba(24, 144, 255, 0.15);
    }

    &.disabled {
      opacity: 0.5;
    }

    .radioCell {
      width: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 8px 0;
    }

    .sequenceCell {
      width: 80px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #fff;
      padding: 8px 0;
    }

    .descriptionCell {
      flex: 1;
      padding: 8px 0 8px 20px;
      color: #fff;
      text-align: left;
    }

    // 选中时的字体颜色变化
    &.selected {
      .sequenceCell,
      .descriptionCell {
        color: #1890ff;
      }
    }
  }
}
