import React, { useEffect, useMemo, forwardRef, useImperativeHandle, useRef, useState } from 'react';
import ReactQuill, { Quill } from 'react-quill';
// @ts-ignore
import ImageResize from 'quill-image-resize-module-react';
import { message } from 'antd';
import 'react-quill/dist/quill.snow.css';
import './styles.less';
import { saveTemplateImage, saveGroupTemplateImage, getImage } from '@/services/reports';
import storeUtil from '@/utils/store';

// 声明全局变量 CLOUD_API
declare const CLOUD_API: string;

interface RichTextEditorProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  style?: React.CSSProperties;
  className?: string;
  readOnly?: boolean;
  disableToolbar?: boolean;
  maxLength?: number;
  isGroupTemplate?: boolean; // 是否为团队模板
  onImageUpload?: (file: File, callback: (imageName: string) => void) => void; // 图片上传回调
  imageUploading?: boolean; // 图片上传状态
  showWordCount?: boolean; // 是否显示字数统计
  wordCountPosition?: 'bottom' | 'top'; // 字数显示位置
}

// Define fonts and sizes
const fontArr = ['SimSun', 'SimHei', 'Microsoft-YaHei', 'KaiTi', 'FangSong'];
const fontSizeArr = ['14px', '16px', '20px', '24px', '36px'];

// 注册自定义字体
const Font = Quill.import('formats/font');
Font.whitelist = fontArr;
Quill.register(Font, true);

// 注册自定义字号
const Size = Quill.import('attributors/style/size');
Size.whitelist = fontSizeArr;
Quill.register(Size, true);

// 注册图片缩放模块
// 确保这行代码在组件定义之前，并且在顶层作用域
Quill.register('modules/imageResize', ImageResize);

// 配置允许的协议，包括blob协议
const Image = Quill.import('formats/image');
const originalSanitize = Image.sanitize;
Image.sanitize = function (url: string) {
  // 允许 blob 和 data URL
  if (url.startsWith('blob:') || url.startsWith('data:')) {
    return url;
  }
  // 其他URL使用原有的清理逻辑
  return originalSanitize ? originalSanitize(url) : url;
};

export interface RichTextEditorRef {
  getEditor: () => any;
  imageMapRef: React.MutableRefObject<Map<string, string>>;
}

const RichTextEditor = forwardRef<RichTextEditorRef, RichTextEditorProps>(({
  value,
  onChange,
  placeholder,
  style,
  className,
  readOnly,
  disableToolbar,
  maxLength,
  isGroupTemplate = false,
  onImageUpload,
  imageUploading = false,
  showWordCount = false,
  wordCountPosition = 'bottom',
}, ref) => {
  const quillRef = useRef<ReactQuill>(null);
  // 添加状态来跟踪是否有文本选中
  const [hasSelection, setHasSelection] = useState(false);
  // 维护图片名称到blob URL的映射
  const imageMapRef = useRef<Map<string, string>>(new Map());
  // 添加字数统计状态
  const [wordCount, setWordCount] = useState(0);

  useImperativeHandle(ref, () => ({
    getEditor: () => quillRef.current?.getEditor(),
    imageMapRef,
  }));

  // 更新工具栏状态的函数
  const updateToolbarState = (quill: any, range: any, hasTextSelected: boolean) => {
    const container = (quill as any).container;
    const toolbar = container?.parentNode?.querySelector('.ql-toolbar');
    if (!toolbar) return;

    // 格式化工具配置
    const formatTools = [
      { selector: '.ql-format-painter', format: null }, // 格式刷需要选中文本
      { selector: '.ql-header-toggle', format: 'header' },
      { selector: '.ql-size-toggle', format: 'size' },
      { selector: '.ql-bold', format: 'bold' },
      { selector: '.ql-italic', format: 'italic' },
      { selector: '.ql-underline', format: 'underline' }
    ];

    // 关闭工具始终可用
    const alwaysEnabledTools = [
      '.ql-undo',
      '.ql-redo',
      '.ql-image',
      '.ql-symbols',
      '.ql-list'
    ];

    if (hasTextSelected) {
      formatTools.forEach(({ selector }) => {
        const button = toolbar.querySelector(selector) as HTMLButtonElement;
        if (button) {
          button.disabled = false;
          button.classList.remove('ql-disabled');
        }
      });
    } else {
      // 没有选中文本时，只有光标处于某格式时允许关闭该格式，否则禁用
      const currentFormat = quill.getFormat(range.index, 0);
      formatTools.forEach(({ selector, format }) => {
        const button = toolbar.querySelector(selector) as HTMLButtonElement;
        if (button) {
          // 格式刷始终禁用
          if (selector === '.ql-format-painter') {
            button.disabled = true;
            button.classList.add('ql-disabled');
          } else if (format && currentFormat[format]) {
            // 光标处于该格式，允许点击关闭
            button.disabled = false;
            button.classList.remove('ql-disabled');
          } else {
            // 其他情况禁用
            button.disabled = true;
            button.classList.add('ql-disabled');
          }
        }
      });
    }

    // 确保关闭工具始终启用
    alwaysEnabledTools.forEach(selector => {
      const button = toolbar.querySelector(selector) as HTMLButtonElement;
      if (button) {
        button.disabled = false;
        button.classList.remove('ql-disabled');
      }
    });
  };

  // 监听选择变化
  useEffect(() => {
    const quill = quillRef.current?.getEditor();
    if (!quill) return;

    const handleSelectionChange = (range: any) => {
      // 检查是否有文本选中（选择长度大于0）
      const hasTextSelected = !!(range && range.length > 0);
      setHasSelection(hasTextSelected);

      // 如果没有选中文本但有光标位置，检查当前格式状态
      if (!hasTextSelected && range) {
        updateToolbarState(quill, range, false);
      } else if (hasTextSelected) {
        updateToolbarState(quill, range, true);
      }
    };

    quill.on('selection-change', handleSelectionChange);

    return () => {
      quill.off('selection-change', handleSelectionChange);
    };
  }, []);

  // 粘贴文本时只插入纯文本，无格式
  useEffect(() => {
    const quill = quillRef.current?.getEditor();
    if (!quill) return;

    // 粘贴文本时只插入纯文本，无格式
    const handlePaste = (e: ClipboardEvent) => {
      if (!e.clipboardData) return;
      const text = e.clipboardData.getData('text/plain');
      if (text) {
        e.preventDefault();
        const selection = quill.getSelection();
        if (selection) {
          quill.insertText(selection.index, text);
          quill.setSelection({ index: selection.index + text.length, length: 0 });
        } else {
          quill.insertText(quill.getLength() - 1, text);
        }
      }
    };
    const editorElem = quill.root;
    editorElem.addEventListener('paste', handlePaste);
    return () => {
      editorElem.removeEventListener('paste', handlePaste);
    };
  }, []);

  // 简化的图片处理 - 直接转换显示，不做复杂的状态管理
  const convertImagePlaceholders = async (content: string): Promise<string> => {
    if (!content || !content.includes('{{IMAGE:')) {
      return content;
    }

    const imageRegex = /\{\{IMAGE:([^}]+)\}\}/g;
    let processedContent = content;
    const matches = Array.from(content.matchAll(imageRegex));

    for (const match of matches) {
      const imageName = match[1];
      const placeholder = match[0];

      try {
        // 检查是否已有缓存的blob URL
        let blobUrl = null;
        for (const [url, name] of imageMapRef.current.entries()) {
          if (name === imageName) {
            blobUrl = url;
            break;
          }
        }

        // 如果没有缓存，获取新的图片
        if (!blobUrl) {
          const blob = await getImage({ ImageName: imageName });
          if (blob) {
            blobUrl = URL.createObjectURL(blob);
            imageMapRef.current.set(blobUrl, imageName);
          }
        }

        if (blobUrl) {
          processedContent = processedContent.replace(placeholder, `<img src="${blobUrl}" alt="${imageName}" />`);
        }
      } catch (error) {
        console.error('转换图片失败:', imageName, error);
      }
    }

    return processedContent;
  };

  // 处理内容显示
  const [displayContent, setDisplayContent] = useState('');

  useEffect(() => {
    const updateDisplayContent = async () => {
      if (value) {
        const converted = await convertImagePlaceholders(value);
        setDisplayContent(converted);
      } else {
        setDisplayContent('');
      }
    };

    updateDisplayContent();
  }, [value]);

  // 更新字数统计
  useEffect(() => {
    if (showWordCount) {
      const count = calculateWordCount(displayContent);
      setWordCount(count);
    }
  }, [displayContent, showWordCount]);

  // 清理资源
  useEffect(() => {
    return () => {
      // 清理所有blob URLs
      for (const blobUrl of imageMapRef.current.keys()) {
        if (blobUrl.startsWith('blob:')) {
          URL.revokeObjectURL(blobUrl);
        }
      }
      // 清理引用
      imageMapRef.current.clear();
    };
  }, []);

  // 获取纯文本字符数
  const getTextLength = (html: string): number => {
    if (!html) return 0;
    const div = document.createElement('div');
    div.innerHTML = html;
    return div.textContent?.length || 0;
  };

  // 计算字数（中文字符算一个字，英文字符算半个字）
  const calculateWordCount = (html: string): number => {
    if (!html) return 0;
    const div = document.createElement('div');
    div.innerHTML = html;
    const text = div.textContent || '';

    let count = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text[i];
      // 中文字符（包括中文标点）
      if (/[\u4e00-\u9fa5\u3000-\u303f\uff00-\uffef]/.test(char)) {
        count += 1;
      } else if (char.trim()) {
        // 非空白英文字符算半个字
        count += 0.5;
      }
    }
    return Math.ceil(count);
  };

  // 截断HTML内容，保持最大字符数限制
  const truncateHtmlContent = (html: string, maxChars: number): string => {
    if (!html || !maxChars) return html;

    const div = document.createElement('div');
    div.innerHTML = html;
    const textContent = div.textContent || '';

    if (textContent.length <= maxChars) {
      return html;
    }

    // 需要截断，创建新的HTML结构
    const truncatedText = textContent.substring(0, maxChars);
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // 递归截断节点内容
    const truncateNode = (node: Node, remainingChars: number): number => {
      if (remainingChars <= 0) {
        // 如果不是图片元素，才清空内容
        if (node.nodeType === Node.ELEMENT_NODE && (node as Element).tagName === 'IMG') {
          return remainingChars; // 保留图片，不消耗字符数
        }
        node.textContent = '';
        return 0;
      }

      if (node.nodeType === Node.TEXT_NODE) {
        const text = node.textContent || '';
        if (text.length <= remainingChars) {
          return remainingChars - text.length;
        } else {
          node.textContent = text.substring(0, remainingChars);
          return 0;
        }
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        // 图片元素不消耗字符数，直接保留
        if ((node as Element).tagName === 'IMG') {
          return remainingChars;
        }

        let remaining = remainingChars;
        const children = Array.from(node.childNodes);
        for (const child of children) {
          remaining = truncateNode(child, remaining);
          if (remaining <= 0) {
            // 移除后续所有兄弟节点，但保留图片
            const nextSiblings = children.slice(children.indexOf(child) + 1);
            nextSiblings.forEach(sibling => {
              if (sibling.nodeType === Node.ELEMENT_NODE && (sibling as Element).tagName !== 'IMG') {
                sibling.remove();
              }
            });
            break;
          }
        }
        return remaining;
      }
      return remainingChars;
    };

    truncateNode(tempDiv, maxChars);
    return tempDiv.innerHTML;
  };

  // 处理输入变化
  const handleChange = (content: string) => {
    // [!code focus start]
    // 立即更新内部显示状态以防止光标跳动和内容闪烁（乐观更新）。
    // 这可以确保UI立即响应用户输入，避免因React的渲染周期延迟导致的问题。
    if (displayContent !== content) {
      setDisplayContent(content);
    }
    // [!code focus end]

    // 更新字数统计
    if (showWordCount) {
      const count = calculateWordCount(content);
      setWordCount(count);
    }

    // 将blob URL转换为图片名称格式保存
    let processedContent = content;

    // 查找所有img标签
    const imgRegex = /<img[^>]*src="([^"]*)"[^>]*>/g;
    let match;

    while ((match = imgRegex.exec(content)) !== null) {
      const imgSrc = match[1];

      // 检查是否为我们的blob URL
      if (imgSrc.startsWith('blob:') && imageMapRef.current.has(imgSrc)) {
        const imageName = imageMapRef.current.get(imgSrc);
        if (imageName) {
          // 替换为图片名称格式
          processedContent = processedContent.replace(match[0], `{{IMAGE:${imageName}}}`);
        }
      }
    }

    if (maxLength) {
      const textLength = getTextLength(processedContent);
      if (textLength > maxLength) {
        // 超过限制时，直接返回，不再触发onChange
        message.warning(`最多只能输入${maxLength}个字符`);
        return;
      }
    }
    onChange?.(processedContent);
  };

  // 限制键盘输入和API插入
  useEffect(() => {
    const quill = quillRef.current?.getEditor();
    if (!quill) return;
    if (!maxLength) return;
    const handleBeforeInput = (delta: any, oldDelta: any, source: any) => {
      if (source !== 'user') return;
      const text = quill.getText();
      const textLength = text ? text.length - 1 : 0; // quill.getText() 末尾有个换行符
      let insertLength = 0;
      delta.ops?.forEach((op: any) => {
        if (op.insert && typeof op.insert === 'string') {
          insertLength += op.insert.length;
        }
      });
      if (textLength + insertLength > maxLength) {
        message.warning(`最多只能输入${maxLength}个字符`);
        return false;
      }
      return true;
    };
    quill.on('text-change', handleBeforeInput);
    return () => {
      quill.off('text-change', handleBeforeInput);
    };
  }, [maxLength]);

  // 获取显示用的内容（可能被截断）
  const getDisplayValue = (): string => {
    if (!displayContent || !maxLength) return displayContent || '';
    return truncateHtmlContent(displayContent, maxLength);
  };

  // [!code delete start]
  // 监听显示内容变化，直接设置到编辑器
  // useEffect(() => {
  //   const editor = quillRef.current?.getEditor();
  //   if (!editor || !displayContent) return;
  //
  //   // 直接设置HTML内容
  //   try {
  //     if (editor.root.innerHTML !== displayContent) {
  //       editor.clipboard.dangerouslyPasteHTML(displayContent);
  //     }
  //   } catch (error) {
  //     console.error('设置编辑器内容失败:', error);
  //     editor.root.innerHTML = displayContent;
  //   }
  // }, [displayContent]);
  // [!code delete end]

  // Memoize the modules configuration
  const modules = useMemo(() => {
    const toolbarConfig = {
      container: [
        // 第一行：撤销恢复、格式刷、字体大小
        [
          { 'undo': '' },
          { 'redo': '' },
          'format-painter', // 格式刷
          'size-toggle', // 字体大小切换
        ],
        // 第二行：文字层级、文字样式
        [
          'header-toggle', // 标题分级切换
          'bold', 'italic', 'underline'
        ],
        // 第三行：列表、插入功能
        [
          { list: 'ordered' },
          { list: 'bullet' },
          'image',
          'symbols' // 自定义常用符号按钮
        ],
      ],
      handlers: {
        'undo': function (this: any) {
          this.quill.history.undo();
        },
        'redo': function (this: any) {
          this.quill.history.redo();
        },
        'image': function (this: any) {
          // 自定义图片上传处理器
          const input = document.createElement('input');
          input.setAttribute('type', 'file');
          input.setAttribute('accept', 'image/*');
          input.click();

          // 保存 quill 实例的引用
          const quillInstance = this.quill;

          input.onchange = async () => {
            const file = input.files?.[0];
            if (file) {
              try {
                // 如果有自定义的图片上传回调，使用它
                if (onImageUpload) {
                  onImageUpload(file, async (imageName: string) => {
                    console.log('富文本编辑器收到图片名称:', imageName);
                    try {
                      // 立即获取图片数据，创建blob URL
                      console.log('开始获取图片数据...');
                      const blob = await getImage({ ImageName: imageName });
                      console.log('获取到的blob:', blob);

                      if (blob) {
                        const blobUrl = URL.createObjectURL(blob);
                        console.log('创建的blob URL:', blobUrl);

                        // 保存映射关系
                        imageMapRef.current.set(blobUrl, imageName);
                        console.log('保存映射关系完成');

                        // 插入图片到编辑器
                        let range = quillInstance.getSelection();
                        console.log('当前选择范围:', range);

                        if (!range) {
                          // 如果没有选择范围，设置光标到末尾
                          const length = quillInstance.getLength();
                          range = { index: length - 1, length: 0 };
                          quillInstance.setSelection(range);
                          console.log('设置光标到末尾位置:', range.index);
                        }

                        console.log('在位置', range.index, '插入图片');

                        // 插入图片
                        quillInstance.insertEmbed(range.index, 'image', blobUrl);

                        // 在图片后插入换行符，确保后续内容能正常输入
                        quillInstance.insertText(range.index + 1, '\n');

                        // 设置光标到图片后的换行符之后
                        quillInstance.setSelection(range.index + 2);
                        console.log('图片插入完成');
                      } else {
                        console.error('获取到的blob为空');
                        message.error('图片加载失败');
                      }
                    } catch (error) {
                      console.error('获取图片失败:', error);
                      message.error('图片加载失败');
                    }
                  });
                } else {
                  // 回退到原有的直接上传逻辑
                  const loadingMessage = message.loading('图片上传中...', 0);

                  // 根据模板类型选择不同的上传接口
                  const response = isGroupTemplate
                    ? await saveGroupTemplateImage(file)
                    : await saveTemplateImage(file);

                  loadingMessage();

                  // 假设返回的数据结构包含图片文件名
                  const imageName = response?.Name;

                  if (imageName) {
                    try {
                      // 立即获取图片数据，创建blob URL
                      const blob = await getImage({ ImageName: imageName });
                      if (blob) {
                        const blobUrl = URL.createObjectURL(blob);

                        // 保存映射关系
                        imageMapRef.current.set(blobUrl, imageName);

                        // 插入图片到编辑器
                        let range = quillInstance.getSelection();

                        if (!range) {
                          // 如果没有选择范围，设置光标到末尾
                          const length = quillInstance.getLength();
                          range = { index: length - 1, length: 0 };
                          quillInstance.setSelection(range);
                        }

                        // 插入图片
                        quillInstance.insertEmbed(range.index, 'image', blobUrl);

                        // 在图片后插入换行符，确保后续内容能正常输入
                        quillInstance.insertText(range.index + 1, '\n');

                        // 设置光标到图片后的换行符之后
                        quillInstance.setSelection(range.index + 2);
                        message.success('图片上传成功');
                      } else {
                        message.error('图片加载失败');
                      }
                    } catch (error) {
                      console.error('获取图片失败:', error);
                      message.error('图片加载失败');
                    }
                  } else {
                    message.error('图片上传失败：返回数据格式错误');
                  }
                }
              } catch (error) {
                console.error('图片上传失败:', error);
                message.error('图片上传失败');
              }
            }
          };
        },
        'format-painter': function (this: any) {
          // 格式刷功能实现
          let formatPainterData: any = null;
          let isFormatPainterActive = false;

          const button = document.querySelector('.ql-format-painter');

          if (!isFormatPainterActive) {
            // 第一次点击：复制格式
            const selection = this.quill.getSelection();
            if (selection && selection.length > 0) {
              formatPainterData = this.quill.getFormat(selection.index, selection.length);
              isFormatPainterActive = true;
              button?.classList.add('ql-active');
              this.quill.root.style.cursor = 'crosshair';

              // 监听下一次选择来应用格式
              const applyFormat = () => {
                const newSelection = this.quill.getSelection();
                if (newSelection && newSelection.length > 0 && formatPainterData) {
                  this.quill.formatText(newSelection.index, newSelection.length, formatPainterData);
                }

                // 重置状态
                isFormatPainterActive = false;
                formatPainterData = null;
                button?.classList.remove('ql-active');
                this.quill.root.style.cursor = 'text';
                this.quill.off('selection-change', applyFormat);
              };

              this.quill.once('selection-change', applyFormat);
            }
          }
        },
        'header-toggle': function (this: any) {
          // 标题分级下拉菜单
          const selection = this.quill.getSelection();
          if (!selection) return;

          const headerOptions = [
            { label: '正文', value: false },
            { label: '标题1', value: 1 },
            { label: '标题2', value: 2 },
            { label: '标题3', value: 3 },
            { label: '标题4', value: 4 },
            { label: '标题5', value: 5 },
            { label: '标题6', value: 6 }
          ];

          const dropdownPanel = document.createElement('div');
          dropdownPanel.style.cssText = `
            position: absolute;
            background: white;
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 4px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            z-index: 1000;
            min-width: 120px;
          `;

          headerOptions.forEach(option => {
            const btn = document.createElement('div');
            btn.textContent = option.label;
            btn.style.cssText = `
              padding: 8px 16px;
              cursor: pointer;
              font-size: ${option.value ? (20 - (option.value as number) * 2) + 'px' : '14px'};
              font-weight: ${option.value ? 'bold' : 'normal'};
              color: #333;
              &:hover { background-color: #f5f5f5; }
            `;
            btn.onmouseover = () => btn.style.backgroundColor = '#f5f5f5';
            btn.onmouseout = () => btn.style.backgroundColor = 'transparent';
            btn.onclick = () => {
              this.quill.format('header', option.value);
              if (dropdownPanel.parentNode) {
                dropdownPanel.parentNode.removeChild(dropdownPanel);
              }
            };
            dropdownPanel.appendChild(btn);
          });

          // 使用当前编辑器容器内的工具栏进行定位
          const currentContainer = this.quill.container.closest('.quill');
          const toolbar = currentContainer?.querySelector('.ql-toolbar');
          const button = currentContainer?.querySelector('.ql-header-toggle');

          if (button && toolbar) {
            const rect = button.getBoundingClientRect();
            dropdownPanel.style.top = (rect.bottom + window.scrollY) + 'px';
            dropdownPanel.style.left = rect.left + 'px';
          }

          document.body.appendChild(dropdownPanel);

          const closePanel = (e: MouseEvent) => {
            if (dropdownPanel.parentNode && !dropdownPanel.contains(e.target as Node)) {
              dropdownPanel.parentNode.removeChild(dropdownPanel);
              document.removeEventListener('click', closePanel);
            }
          };
          setTimeout(() => document.addEventListener('click', closePanel), 100);
        },
        'size-toggle': function (this: any) {
          // 字体大小下拉菜单
          const selection = this.quill.getSelection();
          if (!selection) return;

          const sizeOptions = [
            { label: '14px', value: '14px' },
            { label: '16px', value: '16px' },
            { label: '20px', value: '20px' },
            { label: '24px', value: '24px' },
            { label: '36px', value: '36px' }
          ];

          const dropdownPanel = document.createElement('div');
          dropdownPanel.style.cssText = `
            position: absolute;
            background: white;
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 4px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            z-index: 1000;
            min-width: 80px;
          `;

          sizeOptions.forEach(option => {
            const btn = document.createElement('div');
            btn.textContent = option.label;
            btn.style.cssText = `
              padding: 8px 16px;
              cursor: pointer;
              font-size: ${option.value};
              color: #333;
            `;
            btn.onmouseover = () => btn.style.backgroundColor = '#f5f5f5';
            btn.onmouseout = () => btn.style.backgroundColor = 'transparent';
            btn.onclick = () => {
              this.quill.format('size', option.value);
              if (dropdownPanel.parentNode) {
                dropdownPanel.parentNode.removeChild(dropdownPanel);
              }
            };
            dropdownPanel.appendChild(btn);
          });

          // 使用当前编辑器容器内的工具栏进行定位
          const currentContainer = this.quill.container.closest('.quill');
          const toolbar = currentContainer?.querySelector('.ql-toolbar');
          const button = currentContainer?.querySelector('.ql-size-toggle');

          if (button && toolbar) {
            const rect = button.getBoundingClientRect();
            dropdownPanel.style.top = (rect.bottom + window.scrollY) + 'px';
            dropdownPanel.style.left = rect.left + 'px';
          }

          document.body.appendChild(dropdownPanel);

          const closePanel = (e: MouseEvent) => {
            if (dropdownPanel.parentNode && !dropdownPanel.contains(e.target as Node)) {
              dropdownPanel.parentNode.removeChild(dropdownPanel);
              document.removeEventListener('click', closePanel);
            }
          };
          setTimeout(() => document.addEventListener('click', closePanel), 100);
        },
        'symbols': function (this: any) {
          // 常用医学符号插入
          const symbols = [
            '⁹⁹ᵐTc', '¹⁸F', '¹³¹I', '¹²³I', '⁶⁷Ga', '¹¹¹In',
            'μCi', 'mCi', 'GBq', 'MBq', 'kBq',
            '≥', '≤', '±', '×', '÷', '°C', '%',
            '→', '←', '↑', '↓', '⇒', '⇐'
          ];

          const symbolPanel = document.createElement('div');
          symbolPanel.style.cssText = `
            position: absolute;
            background: white;
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            z-index: 1000;
            max-width: 300px;
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 4px;
          `;

          symbols.forEach(symbol => {
            const btn = document.createElement('button');
            btn.textContent = symbol;
            btn.style.cssText = `
              padding: 4px 8px;
              border: 1px solid #ddd;
              background: white;
              border-radius: 2px;
              cursor: pointer;
              font-size: 12px;
            `;
            btn.onclick = () => {
              const range = this.quill.getSelection();
              if (range) {
                this.quill.insertText(range.index, symbol);
              }
              if (symbolPanel.parentNode) {
                symbolPanel.parentNode.removeChild(symbolPanel);
              }
            };
            symbolPanel.appendChild(btn);
          });

          // 使用当前编辑器容器内的工具栏进行定位
          const currentContainer = this.quill.container.closest('.quill');
          const toolbar = currentContainer?.querySelector('.ql-toolbar');
          const button = currentContainer?.querySelector('.ql-symbols');

          if (button && toolbar) {
            const rect = button.getBoundingClientRect();
            symbolPanel.style.top = (rect.bottom + window.scrollY) + 'px';
            symbolPanel.style.left = rect.left + 'px';
          }

          document.body.appendChild(symbolPanel);

          const closePanel = (e: MouseEvent) => {
            if (symbolPanel.parentNode && !symbolPanel.contains(e.target as Node)) {
              symbolPanel.parentNode.removeChild(symbolPanel);
              document.removeEventListener('click', closePanel);
            }
          };
          setTimeout(() => document.addEventListener('click', closePanel), 100);
        }
      }
    };

    if (readOnly) {
      // @ts-ignore
      delete toolbarConfig.handlers;
    }

    return {
      toolbar: toolbarConfig,
      history: {
        delay: 1000,
        maxStack: 50,
        userOnly: true
      },
      imageResize: {
        parchment: Quill.import('parchment'),
        modules: ['Resize', 'DisplaySize', 'Toolbar']
      },
      clipboard: {
        // 启用匹配视觉效果，保留图片格式
        matchVisual: true,
        matchers: [
          // 明确保留图片元素和其属性
          ['IMG', function (node: any, delta: any, scroll: any) {
            const Delta = Quill.import('delta');
            const src = node.getAttribute('src');
            if (src) {
              console.log('剪贴板处理图片:', src.substring(0, 50) + '...');
              return new Delta().insert({ image: src });
            }
            return delta;
          }]
        ]
      },
      keyboard: {
        bindings: {
          undo: {
            key: 'Z',
            ctrlKey: true,
            handler: function (this: any) { this.quill.history.undo(); }
          },
          redo: {
            key: 'Y',
            ctrlKey: true,
            handler: function (this: any) { this.quill.history.redo(); }
          },
          bold: {
            key: 'B',
            ctrlKey: true,
            handler: function (this: any) { this.quill.format('bold', !this.quill.getFormat()['bold']); }
          },
          italic: {
            key: 'I',
            ctrlKey: true,
            handler: function (this: any) { this.quill.format('italic', !this.quill.getFormat()['italic']); }
          },
          underline: {
            key: 'U',
            ctrlKey: true,
            handler: function (this: any) { this.quill.format('underline', !this.quill.getFormat()['underline']); }
          }
        }
      }
    };
  }, [readOnly]);

  const combinedClassName = `${className ?? ''} ${readOnly ? 'disabled-toolbar' : ''} ${disableToolbar ? 'disable-toolbar-buttons' : ''}`.trim();

  const finalDisplayValue = getDisplayValue();

  return (
    <div style={{ position: 'relative', height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 字数显示 - 顶部 */}
      {showWordCount && wordCountPosition === 'top' && (
        <div className={`word-count-display word-count-top ${maxLength && wordCount > maxLength ? 'word-count-warning' : ''}`}>
          <span className="word-count-text">
            字数：{wordCount}
            {maxLength && (
              <span className="word-count-limit"> / {maxLength}</span>
            )}
          </span>
        </div>
      )}

      <ReactQuill
        ref={quillRef}
        theme="snow"
        value={finalDisplayValue}
        onChange={handleChange}
        placeholder={placeholder}
        modules={modules}
        style={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          ...style
        }}
        className={combinedClassName}
        readOnly={readOnly}
      />

      {/* 字数显示 - 底部 */}
      {showWordCount && wordCountPosition === 'bottom' && (
        <div className={`word-count-display word-count-bottom ${maxLength && wordCount > maxLength ? 'word-count-warning' : ''}`}>
          <span className="word-count-text">
            字数：{wordCount}
            {maxLength && (
              <span className="word-count-limit"> / {maxLength}</span>
            )}
          </span>
        </div>
      )}
    </div>
  );
});

export default RichTextEditor;