.fusionModalContent {
  .seriesListHeader {
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #444;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #fff;
    }
  }

  .seriesListContainer {
    height: 610px;
    overflow-y: auto;
    padding: 0;
    border: none;
    border-radius: 4px;
    background-color: #262626;

    :global {
      .ant-radio-wrapper {
        display: block;
        margin: 0;
        padding: 6px 8px;
        border-radius: 0;
        transition: all 0.3s;
        color: #fff;

        &:hover {
          background-color: #444;
        }

        .ant-radio-inner {
          background-color: transparent;
          border-color: #666;
        }

        .ant-radio-checked .ant-radio-inner {
          border-color: #1890ff;

          &::after {
            background-color: #1890ff;
          }
        }
      }

      .ant-empty-description {
        color: #999;
      }
    }
  }

  .matchedSeries {
    :global {
      .ant-radio-wrapper {
        background-color: rgba(24, 144, 255, 0.15);
        border: 1px solid rgba(24, 144, 255, 0.5);

        &:hover {
          background-color: rgba(24, 144, 255, 0.25);
        }
      }
    }
  }

  .disabledText {
    color: #666;
  }

  .modalFooter {
    margin-top: 24px;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }

  // 列表标题样式
  .seriesListHeader {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #444;
    background-color: #333233;
    font-weight: 500;
    color: #fff;

    .radioWrapper {
      margin-right: 195px;
      flex-shrink: 0;
      width: 16px;
    }

    .seriesNumber {
      width: 60px;
      margin-right: 368px;
      flex-shrink: 0;
    }

    .seriesDescription {
      flex: 1;
    }
  }

  // 列表样式
  .seriesList {
    .seriesItem {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid #444;
      background-color: #262626;
      transition: all 0.3s;

      &:hover {
        background-color: #2a2a2a;
      }

      &.selected {
        .seriesNumber,
        .seriesDescription {
          color: #1890ff;
        }
      }

      .radioWrapper {
        margin-right: 183px;
        flex-shrink: 0;
      }

      .seriesNumber {
        width: 60px;
        color: #fff;
        font-weight: 500;
        margin-right: 368px;
        flex-shrink: 0;
      }

      .seriesDescription {
        flex: 1;
        color: #fff;
        font-size: 14px;
        line-height: 1.4;
      }
    }
  }
}