import React, { useEffect, useRef, useState, useMemo, useCallback, Key } from 'react';
import { connect, type Dispatch } from 'umi';
import { message, Button, Select } from 'antd';
import { CaretRightOutlined, PauseOutlined } from '@ant-design/icons';
import * as cornerstoneTools from '@cornerstonejs/tools';
import { Enums, volumeLoader, utilities as csUtils } from '@cornerstonejs/core';
import { IRenderingEngine, IVolumeViewport } from '@cornerstonejs/core/dist/types/types';
import { getColormap, getColormapNames, registerColormap } from '@cornerstonejs/core/src/utilities/colormap';
import type { PrintType } from '@/models/prints';
import type { ViewType } from '@/models/views';
import { IMouseLeftToolType } from '@/utils/enums';
import { colormaps } from '@/utils/colormaps';
import { changeTool } from '@/utils/toolSwitcher';
// 修改导入方式，确保正确导入
import { initCornerstone } from '@/components/prints/cornerstone';
import CornerInfoOverlay from '../CornerInfoOverlay';
import DirectionIndicator from '../DirectionIndicator';
import SeriesControls from './SeriesControls';
import ViewControls from './ViewControls';
import styles from './Dicom2D.less';

const { Option } = Select;
// 工具名称映射：左侧工具栏使用的简化名称 -> cornerstone实际工具名称
const getActualToolName = (toolName: string): string => {
  const toolNameMap: Record<string, string> = {
    Length: LengthTool.toolName,
    Angle: AngleTool.toolName,
    Probe: ProbeTool.toolName,
    RectangleROI: RectangleROITool.toolName,
    EllipticalROI: EllipticalROITool.toolName,
    PlanarFreehandROI: PlanarFreehandROITool.toolName,
    ArrowAnnotate: ArrowAnnotateTool.toolName,
    Crosshairs: CrosshairsTool.toolName,
    WindowLevel: WindowLevelTool.toolName,
    Pan: PanTool.toolName,
    Zoom: ZoomTool.toolName,
    Text: 'Text', // 特殊工具，保持原名
  };
  return toolNameMap[toolName] || toolName;
};

interface IDicomRenderProps {
  dispatch: Dispatch;
  key: Key;
  renderIndex: number;
  row: number;
  col: number;
  seriesId: string;
  curInstances: Map<string, any[]>;

  getRenderingEngine: (seriesId: string) => IRenderingEngine | undefined;
  orientation: string;
  // MIP相关属性
  mipEnabled?: boolean;
  mipType?: string;
  axialValue?: number;
  coronalValue?: number;
  sagittalValue?: number;
  seriesLists?: any[];
  onSeriesChange?: (seriesId: string) => void;
  onOrientationChange?: (orientation: string) => void;
}

const { ViewportType } = Enums;

const {
  ToolGroupManager,
  addTool,
  PanTool,
  ZoomTool,
  CrosshairsTool,
  WindowLevelTool,
  StackScrollMouseWheelTool,
  StackScrollTool,
  LengthTool,
  AngleTool,
  ProbeTool,
  RectangleROITool,
  CircleROITool,
  EllipticalROITool,
  PlanarFreehandROITool,
  ArrowAnnotateTool,
  Enums: csToolsEnums,
  utilities: csToolsUtilities,
} = cornerstoneTools;




const { MouseBindings } = csToolsEnums;

type Props = IDicomRenderProps & {
  mouseLeftTool: IMouseLeftToolType;
  selectedColorMap?: string;
  resetViewport: boolean;
  windowWidth: number;
  windowCenter: number;
  annotationDeletedTrigger?: boolean;
  cornerInfoConfig?: any;
  InstanceTag?: any[];
  directionIndicatorVisible?: boolean;
  sensitiveInfoHidden?: boolean;
  currentTool?: string;
  views: ViewType; // 添加 views 属性
  // MIP相关属性已经在 IDicomRenderProps 中定义
  seriesLists?: any[];
  curSeriesId?: string;
};

// 全局标志，确保工具只被注册一次
let toolsRegistered = false;

// 全局工具初始化函数
const initializeGlobalTools = () => {
  if (toolsRegistered) return;

  try {
    // 确保工具被全局注册
    const tools = [
      cornerstoneTools.PanTool,
      cornerstoneTools.ZoomTool,
      cornerstoneTools.CrosshairsTool,
      cornerstoneTools.WindowLevelTool,
      cornerstoneTools.StackScrollMouseWheelTool,
      cornerstoneTools.StackScrollTool,
      cornerstoneTools.LengthTool,
      cornerstoneTools.AngleTool,
      cornerstoneTools.ProbeTool,
      cornerstoneTools.RectangleROITool,
      cornerstoneTools.CircleROITool,
      cornerstoneTools.EllipticalROITool,
      cornerstoneTools.PlanarFreehandROITool,
      cornerstoneTools.ArrowAnnotateTool,
    ];

    tools.forEach((tool) => {
      try {
        cornerstoneTools.addTool(tool);
        console.log(`成功注册工具: ${tool.toolName}`);
      } catch (error) {
        console.warn(`工具 ${tool.toolName} 注册失败:`, error);
      }
    });
    toolsRegistered = true;
    console.log('全局工具注册完成');
  } catch (error) {
    console.error('全局工具注册失败:', error);
  }
};

const Dicom2D: React.FC<Props> = React.memo((props) => {
  const {
    dispatch,
    row = 1,
    col = 1,
    renderIndex,
    curInstances,
    //isInverse = false,
    seriesId,
    //space = 1,
    //onWheelHandler,
    getRenderingEngine,
    orientation,
    mouseLeftTool,
    selectedColorMap,
    resetViewport,
    windowWidth,
    windowCenter,
    annotationDeletedTrigger,
    cornerInfoConfig,
    InstanceTag,
    directionIndicatorVisible,
    sensitiveInfoHidden,
    currentTool,
    // MIP相关属性
    mipEnabled,
    mipType,
    axialValue,
    coronalValue,
    sagittalValue,
    curSeriesId,
    seriesLists,
    onSeriesChange,
    onOrientationChange,
  } = props;
  // 去除 injector
  const newSeriesLists = useMemo(() => seriesLists?.filter(item => item.SeriesDescription !== 'injector') || [], [seriesLists]);
  const currentIndex = useMemo(() => {
    return newSeriesLists.findIndex(item => item.SeriesId === curSeriesId)
  }, [curSeriesId]);

  const rendererRef = useRef<HTMLDivElement>(null);
  const [isInitializing, setIsInitializing] = useState(false); // 防止重复初始化

  const [isPlaying, setIsPlaying] = useState(false);
  const [framesPerSecond, setFramesPerSecond] = useState(30); // 默认 30 FPS

  // 添加鼠标位置和像素值的状态
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [pixelValue, setPixelValue] = useState('0');
  const [showProbe, setShowProbe] = useState(false); // 是否显示点测量信息
  const mouseDownRef = useRef(false);

  // 处理鼠标移动事件，获取鼠标位置和像素值
  const handleMouseMove = useCallback(
    async (event: MouseEvent) => {
      if (!rendererRef.current) return;

      // 获取元素相对于视口的位置
      const rect = rendererRef.current.getBoundingClientRect();

      // 计算鼠标在元素内的相对位置
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      // 更新鼠标位置状态
      setMousePosition({ x, y });

      // 检查是否需要更新像素值（四角信息配置中勾选了像素值，或者Probe工具激活）
      const shouldUpdatePixelValue =
        (cornerInfoConfig?.ct?.pixelValue && getImageType() === 'ct') ||
        (cornerInfoConfig?.pet?.pixelValue && getImageType() === 'pet') ||
        (cornerInfoConfig?.fusion?.pixelValue && getImageType() === 'fusion') ||
        currentTool === 'Probe';

      if (!shouldUpdatePixelValue) {
        return;
      }

      // 获取当前视口
      const viewportId = `STACK_${renderIndex}`;
      const curRenderingEngine = getRenderingEngine(String(seriesId));
      if (!curRenderingEngine) return;

      const viewport = curRenderingEngine.getViewport(viewportId) as IVolumeViewport;
      if (!viewport) return;

      try {
        console.log('鼠标位置:', { x, y });

        // 将canvas坐标转换为世界坐标
        const canvasPos = [x, y] as [number, number];
        const worldPos = viewport.canvasToWorld(canvasPos);
        console.log('世界坐标:', worldPos);

        // 尝试获取当前显示的volume（3D图像）
        const volumes = (viewport as any).getVolumes?.();
        if (volumes && volumes.length > 0) {
          const volume = volumes[0]; // 获取第一个volume
          console.log('获取到的volume:', volume);

          if (volume && volume.imageData) {
            const { dimensions, imageData } = volume;
            console.log('volume维度:', dimensions);

            // 将世界坐标转换为体素索引
            const index = imageData.worldToIndex(worldPos);
            console.log('体素索引:', index);

            // 向下取整索引值
            index[0] = Math.floor(index[0]);
            index[1] = Math.floor(index[1]);
            index[2] = Math.floor(index[2]);

            // 检查索引是否在图像维度范围内
            if (!csUtils.indexWithinDimensions(index, dimensions)) {
              console.log('索引超出范围');
              setPixelValue('N/A');
              return;
            }

            // 使用voxelManager获取像素值
            if (volume.voxelManager) {
              const value = volume.voxelManager.getAtIJK(index[0], index[1], index[2]);
              console.log('获取到的像素值:', value);

              if (value !== undefined && value !== null) {
                setPixelValue(value.toFixed(3));
                return;
              }
            } else {
              console.log('volume没有voxelManager');
            }
          } else {
            console.log('volume没有imageData');
          }
        } else {
          console.log('viewport没有volumes，尝试2D图像方法');

          // 对于2D图像，尝试使用不同的方法
          // 尝试获取当前图像数据
          const imageData = (viewport as any).getImageData?.();
          if (imageData) {
            console.log('获取到2D图像数据:', imageData);

            // 尝试获取像素数据
            let scalarData = null;

            // 首先尝试使用getScalarData方法
            if (imageData.getScalarData) {
              scalarData = imageData.getScalarData();
              console.log('通过getScalarData方法获取到scalarData');
            }

            // 如果方法不存在，尝试直接访问scalarData属性
            if (!scalarData && imageData.scalarData) {
              scalarData = imageData.scalarData;
              console.log('通过scalarData属性获取到数据');
            }

            if (scalarData) {
              console.log('获取到scalarData，长度:', scalarData.length);

              // 获取canvas信息
              const canvas = (viewport as any).getCanvas?.();
              if (canvas) {
                const rect = canvas.getBoundingClientRect();
                console.log('canvas尺寸:', { width: canvas.width, height: canvas.height });

                // 计算像素索引
                const canvasX = Math.floor((x / rect.width) * canvas.width);
                const canvasY = Math.floor((y / rect.height) * canvas.height);
                console.log('计算后的canvas坐标:', { canvasX, canvasY });

                // 确保坐标在有效范围内
                if (canvasX >= 0 && canvasX < canvas.width && canvasY >= 0 && canvasY < canvas.height) {
                  const index = canvasY * canvas.width + canvasX;
                  console.log('计算的一维索引:', index);

                  if (index < scalarData.length) {
                    const value = scalarData[index];
                    console.log('通过2D方法获取到像素值:', value);
                    if (value !== undefined) {
                      setPixelValue(value.toFixed(3));
                      return;
                    }
                  } else {
                    console.log('索引超出scalarData范围');
                  }
                } else {
                  console.log('canvas坐标超出范围');
                }
              } else {
                console.log('无法获取canvas');
              }
            } else {
              console.log('scalarData为空');
            }
          } else {
            console.log('imageData没有getScalarData方法');
          }
        }

        // 如果所有方法都失败，显示位置信息
        console.log('无法获取像素值，显示位置信息');
        setPixelValue(`Pos: ${x.toFixed(0)}, ${y.toFixed(0)}`);
      } catch (error) {
        console.error('获取像素值失败:', error);
        setPixelValue('N/A');
      }
    },
    [renderIndex, seriesId, getRenderingEngine, currentTool, cornerInfoConfig]
  );

  // 添加和移除鼠标移动事件监听器
  useEffect(() => {
    if (!rendererRef.current) return;

    // 添加鼠标移动事件监听器
    rendererRef.current.addEventListener('mousemove', handleMouseMove as any);

    // 清理函数
    return () => {
      if (rendererRef.current) {
        rendererRef.current.removeEventListener('mousemove', handleMouseMove as any);
      }
    };
  }, [handleMouseMove, rendererRef]);

  // 当工具切换时，清除点测量状态
  useEffect(() => {
    if (currentTool !== 'Probe') {
      setShowProbe(false);
      setPixelValue('0');
    }
  }, [currentTool]);

  // useEffect(() => {
  //   console.log('--seriesId变化了---', seriesId);
  // }, [seriesId]);

  // ColorMap变化时，重新设置colormap
  useEffect(() => {
    console.log('selectedColorMap变化:', selectedColorMap);
    if (!imageIds || !imageIds.length) {
      console.log('未加载图像，无法应用伪彩');
      return;
    }
    if (!selectedColorMap) {
      console.log('未选择伪彩方案');
      return;
    }
    if (!rendererRef.current) {
      console.log('渲染器未初始化，无法应用伪彩');
      return;
    }

    // 检查渲染器初始化状态
    if (isInitializing) {
      console.warn(`渲染器正在初始化中，暂时无法应用伪彩: ${selectedColorMap}`);
      // 设置延迟尝试应用伪彩
      setTimeout(() => {
        console.log(`延迟尝试应用伪彩: ${selectedColorMap}`);
        dispatch({
          type: 'views/save',
          payload: { selectedColorMap: selectedColorMap },
        });
      }, 1000);
      return;
    }

    const curRenderingEngine = getRenderingEngine(String(seriesId));
    if (!curRenderingEngine) {
      console.error('渲染引擎未找到，无法应用伪彩');
      return;
    }

    const viewportId = `STACK_${renderIndex}`;
    let viewport;

    try {
      viewport = curRenderingEngine.getViewport(viewportId);
    } catch (error) {
      console.error(`获取视口失败: ${viewportId}`, error);
      return;
    }

    if (!viewport) {
      console.error(`视口未找到: ${viewportId}`);
      return;
    }

    try {
      // 验证伪彩方案是否存在
      const colormap = getColormap(selectedColorMap);
      if (!colormap) {
        console.warn(`伪彩方案不存在: ${selectedColorMap}，尝试重新注册`);
        // 尝试重新注册所有伪彩方案
        let registered = false;
        colormaps.forEach((cm) => {
          if (cm.name === selectedColorMap || cm.Name === selectedColorMap) {
            try {
              registerColormap(cm);
              console.log(`重新注册伪彩方案成功: ${cm.name}`);
              registered = true;
            } catch (regError) {
              console.error(`重新注册伪彩方案失败: ${cm.name}`, regError);
            }
          }
        });

        // 再次检查是否注册成功
        if (registered) {
          const verifyColormap = getColormap(selectedColorMap);
          if (!verifyColormap) {
            console.error(`伪彩方案注册后仍然无法获取: ${selectedColorMap}`);
          } else {
            console.log(`伪彩方案重新注册后验证成功: ${selectedColorMap}`);
          }
        } else {
          console.error(`未找到匹配的伪彩方案定义: ${selectedColorMap}`);
        }
      } else {
        console.log(`伪彩方案存在，准备应用: ${selectedColorMap}`);
        console.log('伪彩方案详情:', {
          ColorSpace: colormap.ColorSpace,
          Name: colormap.Name,
          RGBPoints: Array.isArray(colormap.RGBPoints) ? `${colormap.RGBPoints.length} points` : 'undefined',
        });
      }

      // 应用伪彩方案
      console.log(`尝试应用伪彩方案: ${selectedColorMap}`);
      viewport.setProperties({
        colormap: {
          name: selectedColorMap,
        },
      });

      viewport.render();
      console.log('颜色映射已应用:', selectedColorMap);

      // 延迟再次渲染，确保伪彩完全应用
      setTimeout(() => {
        try {
          // 再次确认伪彩应用
          viewport.setProperties({
            colormap: {
              name: selectedColorMap,
            },
          });
          viewport.render();
          console.log('伪彩延迟渲染完成');
        } catch (e) {
          console.warn('伪彩延迟渲染失败:', e);
        }
      }, 200);
    } catch (error) {
      console.error('设置颜色映射失败:', error);
      // 尝试使用备用方法
      try {
        console.log('尝试使用备用方法应用颜色映射...');
        (viewport as any).setProperties({
          colormap: {
            name: selectedColorMap,
          },
        });
        viewport.render();
        console.log('使用备用方法应用颜色映射成功');

        // 延迟再次渲染
        setTimeout(() => {
          try {
            (viewport as any).setProperties({
              colormap: {
                name: selectedColorMap,
              },
            });
            viewport.render();
            console.log('备用方法伪彩延迟渲染完成');
          } catch (e) {
            console.warn('备用方法伪彩延迟渲染失败:', e);
          }
        }, 200);
      } catch (backupError) {
        console.error('备用方法应用颜色映射也失败:', backupError);
      }
    }
  }, [selectedColorMap, imageIds, seriesId, renderIndex, isInitializing, dispatch]);

  //注册colormap
  useEffect(() => {
    console.log('开始注册所有伪彩方案...');
    // 注册所有自定义颜色映射
    colormaps.forEach((colormap) => {
      try {
        // 检查是否已经注册过该伪彩方案
        const existingColormap = getColormap(colormap.name);
        if (existingColormap) {
          console.log(`伪彩方案已存在，跳过注册: ${colormap.name}`);
        } else {
          registerColormap(colormap);
          console.log('已注册颜色映射:', colormap.name);
        }
      } catch (error) {
        console.warn('注册颜色映射失败:', colormap.name, error);
        // 尝试使用备用方法注册
        try {
          // 使用备用方法注册
          const colormapRegistration = {
            name: colormap.name,
            colormap: colormap,
          };
          (registerColormap as any)(colormapRegistration);
          console.log('使用备用方法注册颜色映射成功:', colormap.name);
        } catch (backupError) {
          console.error('备用方法注册颜色映射也失败:', colormap.name, backupError);
        }
      }
    });

    // 获取所有已注册的 colormap 名称
    const colormapNames = getColormapNames();
    console.log('已注册的颜色映射列表:', colormapNames);

    // 验证每个 colormap 是否可以正确获取
    colormapNames.forEach((name) => {
      try {
        const colormap = getColormap(name);
        if (!colormap) {
          console.warn('无法获取颜色映射:', name);
        } else {
          console.log(`成功获取颜色映射: ${name}`);
          // 打印颜色映射的详细信息，帮助调试
          console.log(`颜色映射详情 - ${name}:`, {
            ColorSpace: colormap.ColorSpace,
            Name: colormap.Name,
            RGBPoints: Array.isArray(colormap.RGBPoints) ? `${colormap.RGBPoints.length} points` : 'undefined',
          });
        }
      } catch (error) {
        console.error(`获取颜色映射时出错: ${name}`, error);
      }
    });

    // 特别检查LeftToolBar中使用的伪彩方案是否都已注册
    const requiredColormaps = ['Grayscale', 'X Ray', 'hsv', 'jet', 'suv'];
    requiredColormaps.forEach((name) => {
      try {
        const colormap = getColormap(name);
        if (!colormap) {
          console.error(`关键伪彩方案未注册: ${name}`);
          // 尝试查找匹配的伪彩方案
          const matchingColormap = colormaps.find((cm) => cm.name === name || cm.Name === name);
          if (matchingColormap) {
            try {
              registerColormap(matchingColormap);
              console.log(`成功注册关键伪彩方案: ${name}`);
            } catch (regError) {
              console.error(`注册关键伪彩方案失败: ${name}`, regError);
            }
          } else {
            console.error(`找不到匹配的伪彩方案定义: ${name}`);
          }
        } else {
          console.log(`关键伪彩方案已注册: ${name}`);
        }
      } catch (error) {
        console.error(`检查关键伪彩方案时出错: ${name}`, error);
      }
    });
  }, []);

  // 监听 resetViewport 变化
  useEffect(() => {
    const volumeId = `STACK_${seriesId}`;
    if (resetViewport && volumeId && rendererRef.current) {
      const curRenderingEngine = getRenderingEngine(String(seriesId));
      const viewportId = `STACK_${renderIndex}`;
      const viewport = curRenderingEngine?.getViewport(viewportId) as IVolumeViewport;

      if (viewport && volumeId) {
        viewport.resetProperties(volumeId);
        viewport.render();

        // 重置标志位
        dispatch({
          type: 'views/save',
          payload: { resetViewport: false },
        });
      }
    }
  }, [resetViewport]);

  //  监听 windowWidth 、windowCenter 变化
  useEffect(() => {
    if (!windowWidth || !windowCenter) return;

    if (rendererRef.current) {
      try {
        console.log(`应用窗宽窗位: 窗宽=${windowWidth}, 窗位=${windowCenter}`);

        const curRenderingEngine = getRenderingEngine(String(seriesId));
        if (!curRenderingEngine) {
          console.error('渲染引擎未找到，无法应用窗宽窗位');
          return;
        }

        const viewportId = `STACK_${renderIndex}`;
        let viewport;

        try {
          viewport = curRenderingEngine.getViewport(viewportId);
        } catch (error) {
          console.error(`获取视口失败: ${viewportId}`, error);
          return;
        }

        if (!viewport) {
          console.error(`视口未找到: ${viewportId}`);
          return;
        }

        // 计算窗宽窗位范围
        const lower = windowCenter - windowWidth / 2;
        const upper = windowCenter + windowWidth / 2;
        console.log(`计算窗宽窗位范围: lower=${lower}, upper=${upper}`);

        // 应用窗宽窗位设置
        try {
          viewport.setProperties({
            voiRange: {
              lower,
              upper,
            },
          });
          console.log('成功设置窗宽窗位');

          // 如果有伪彩设置，在窗宽窗位变化后重新应用
          if (selectedColorMap) {
            console.log(`窗宽窗位变化后重新应用伪彩: ${selectedColorMap}`);
            try {
              viewport.setProperties({
                colormap: {
                  name: selectedColorMap,
                },
              });
              console.log('窗宽窗位变化后伪彩应用成功');
            } catch (colorError) {
              console.error('窗宽窗位变化后应用伪彩失败:', colorError);
            }
          }

          // 强制渲染视口
          viewport.render();
          console.log('窗宽窗位设置完成并渲染');

          // 额外的渲染保证
          setTimeout(() => {
            try {
              // 再次确认伪彩应用
              if (selectedColorMap) {
                console.log(`延迟渲染前再次确认伪彩: ${selectedColorMap}`);
                try {
                  viewport.setProperties({
                    colormap: {
                      name: selectedColorMap,
                    },
                  });
                } catch (colorError) {
                  console.error('延迟渲染前应用伪彩失败:', colorError);
                }
              }

              viewport.render();
              console.log('延迟渲染完成');
            } catch (e) {
              console.warn('延迟渲染失败:', e);
            }
          }, 100);
        } catch (e) {
          console.error('设置窗宽窗位失败:', e);

          // 尝试备用方法
          try {
            console.log('尝试备用方法设置窗宽窗位...');
            const volumeViewport = viewport as IVolumeViewport;
            const volumeId = `STACK_${seriesId}`;

            volumeViewport.setProperties(
              {
                voiRange: {
                  lower,
                  upper,
                },
              },
              volumeId
            );

            // 如果有伪彩设置，在窗宽窗位变化后重新应用
            if (selectedColorMap) {
              console.log(`备用方法：窗宽窗位变化后重新应用伪彩: ${selectedColorMap}`);
              try {
                volumeViewport.setProperties(
                  {
                    colormap: {
                      name: selectedColorMap,
                    },
                  },
                  volumeId
                );
                console.log('备用方法：窗宽窗位变化后伪彩应用成功');
              } catch (colorError) {
                console.error('备用方法：窗宽窗位变化后应用伪彩失败:', colorError);
              }
            }

            volumeViewport.render();
            console.log('备用方法设置窗宽窗位成功');
          } catch (backupError) {
            console.error('备用方法设置窗宽窗位也失败:', backupError);
          }
        }
      } catch (error) {
        console.error('设置窗宽窗位时发生错误:', error);
      }
    }
  }, [windowWidth, windowCenter, seriesId, renderIndex, props.views?.windowLevelChanged]);

  useEffect(() => {
    // console.log('--annotationDeletedTrigger:---', annotationDeletedTrigger);
    const curRenderingEngine = getRenderingEngine(String(seriesId));
    const viewportId = `STACK_${renderIndex}`;
    if (annotationDeletedTrigger && rendererRef.current) {
      const viewport = curRenderingEngine?.getViewport(viewportId) as IVolumeViewport;
      if (viewport) {
        viewport.render();
      }
    }
  }, [annotationDeletedTrigger]);

  //获取imageId
  const imageIds = useMemo(() => {
    if (!seriesId) return undefined;
    const instanceList = curInstances.get(String(seriesId));
    // console.log('instanceList有无--instanceList', instanceList);
    if (!instanceList || !Array.isArray(instanceList)) return [];
    const originImageIds = instanceList.map((instance: any) => {
      // console.log('instanceId', instance);
      // return `wadouri:${CLOUD_API}/v1?Action=GetDicomFileInPrint&Version=${VERSION}&InstanceId=${instance.Id}`;
      return `wadouri:${CLOUD_API}/public/getInstanceDicom?InstanceId=${instance.Id}`;
    });
    // console.log('originImageIds', originImageIds);
    // 如果需要反转图像顺序（isInverse），可以在这里处理
    // return isInverse ? originImageIds.reverse() : originImageIds;
    return originImageIds;
  }, [curInstances, rendererRef, seriesId]);

  const loadImages = async () => {
    if (!imageIds || !imageIds.length) return;
    if (rendererRef.current) {
      try {
        console.log('开始加载图像...');
        const viewportId = `STACK_${renderIndex}`;
        const volumeId = `STACK_${seriesId}`;

        // 创建视口配置
        const viewportInput = {
          viewportId,
          element: rendererRef.current,
          type: ViewportType.ORTHOGRAPHIC,
          defaultOptions: {
            orientation: orientation as any,
            background: [0, 0, 0] as [number, number, number], // 设置黑色背景
          },
        };

        // 获取渲染引擎
        const curRenderingEngine = getRenderingEngine(String(seriesId));
        if (!curRenderingEngine) {
          console.error('渲染引擎未找到，无法加载图像');
          return;
        }

        // 启用元素
        curRenderingEngine.enableElement(viewportInput);
        console.log(`视口元素已启用: ${viewportId}`);

        // 获取视口
        const viewport = curRenderingEngine.getViewport(viewportId) as IVolumeViewport;
        if (!viewport) {
          console.error('视口创建失败');
          return;
        }

        try {
          // 创建并缓存卷
          console.log(`创建卷: ${volumeId}, 图像数量: ${imageIds.length}`);
          const volume = await volumeLoader.createAndCacheVolume(volumeId, {
            imageIds,
          });

          // 加载卷
          await volume.load();
          console.log('卷加载完成');

          // 设置卷到视口
          viewport.setVolumes([{ volumeId }]);
          console.log('卷已设置到视口');

          // 如果有窗宽窗位设置，应用它们
          if (windowWidth && windowCenter) {
            console.log(`应用初始窗宽窗位: 窗宽=${windowWidth}, 窗位=${windowCenter}`);
            const lower = windowCenter - windowWidth / 2;
            const upper = windowCenter + windowWidth / 2;

            viewport.setProperties({
              voiRange: {
                lower,
                upper,
              },
            });
          }

          // 如果有伪彩设置，应用它们
          if (selectedColorMap) {
            console.log(`应用初始伪彩: ${selectedColorMap}`);
            viewport.setProperties({
              colormap: {
                name: selectedColorMap,
              },
            });
          }

          // 渲染视口
          curRenderingEngine.render();
          console.log('视口渲染完成');

          // 延迟再次渲染，确保完全加载
          setTimeout(() => {
            try {
              curRenderingEngine.render();
              console.log('延迟渲染完成');
            } catch (e) {
              console.warn('延迟渲染失败:', e);
            }
          }, 200);
        } catch (error) {
          message.error('视口渲染失败请重试');
          console.error('加载图像失败:', error);
        }
      } catch (error) {
        console.error('加载图像过程中发生错误:', error);
      }
    }
  };

  // 合并的useEffect，统一处理图像加载和工具组设置
  useEffect(() => {
    const initializeViewport = async () => {
      if (imageIds && imageIds.length > 0 && rendererRef.current && seriesId && !isInitializing) {
        setIsInitializing(true);
        console.log(`开始初始化视口: ${seriesId}`);

        const renderingEngine = getRenderingEngine(String(seriesId));
        console.log(`获取渲染引擎: ${renderingEngine}`);
        if (renderingEngine) {
          try {
            await loadImages();

            // 延迟一点时间确保视口完全创建后再设置工具组
            setTimeout(async () => {
              await setUpToolGroups();

              // 确保默认工具被应用
              setTimeout(() => {
                console.log(`应用默认工具: ${currentTool || 'Crosshairs'}`);
                if (currentTool) {
                  reApplyCurrentTool();
                } else {
                  // 如果currentTool为空，强制设置为Crosshairs
                  dispatch({
                    type: 'views/save',
                    payload: { currentTool: 'Crosshairs' },
                  });
                }
              }, 200);

              setIsInitializing(false);
              console.log(`视口初始化完成: ${seriesId}`);
            }, 100);
          } catch (error) {
            console.error(`视口初始化失败: ${seriesId}`, error);
            setIsInitializing(false);
          }
        } else {
          setIsInitializing(false);
        }
      }
    };

    initializeViewport();
  }, [imageIds, getRenderingEngine, rendererRef, seriesId]);

  // 监听seriesId变化，在序列切换时重新应用工具
  useEffect(() => {
    if (seriesId && rendererRef.current && currentTool && !isInitializing) {
      // 给足够的时间让新序列完全加载
      const timer = setTimeout(() => {
        console.log(`序列切换到: ${seriesId}, 重新应用当前工具: ${currentTool}`);
        reApplyCurrentTool();
      }, 800); // 增加延迟时间确保工具组完全设置好

      return () => clearTimeout(timer);
    }
  }, [seriesId, currentTool, isInitializing]);

  // 监听currentTool变化，确保工具状态实时更新
  useEffect(() => {
    if (currentTool && seriesId && !isInitializing) {
      console.log(`工具变化: ${currentTool}，重新应用工具`);
      const timer = setTimeout(() => {
        reApplyCurrentTool();
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [currentTool, seriesId, isInitializing]);

  // 获取实例信息，用于显示四角信息
  const currentInstanceInfo = useMemo(() => {
    if (!InstanceTag || !seriesId) {
      console.log('四角信息数据缺失:', { hasInstanceTag: !!InstanceTag, seriesId });
      return null;
    }

    console.log('四角信息数据获取成功:', { seriesId, tagCount: Object.keys(InstanceTag).length });
    return InstanceTag;
  }, [InstanceTag, seriesId]);

  // 添加一个useEffect来处理实例标签数据的获取
  useEffect(() => {
    if (seriesId && curInstances) {
      const instances = curInstances.get(String(seriesId));
      if (instances && instances.length > 0) {
        const firstInstanceId = instances[0].Id;
        console.log(`获取序列 ${seriesId} 的第一个实例标签数据: ${firstInstanceId}`);

        // 延迟获取，确保组件已完全初始化
        setTimeout(() => {
          dispatch({
            type: 'views/FetchInstanceAllTag',
            payload: firstInstanceId,
          });
        }, 500);
      }
    }
  }, [seriesId, curInstances, dispatch]);

  //
  const reApplyCurrentTool = () => {
    try {
      // console.log(`正在应用工具: ${currentTool}`);
      if (!currentTool) return;
      // 获取渲染引擎
      const curRenderingEngine = getRenderingEngine(String(seriesId));
      // console.error(`渲染引擎未找到: ${seriesId}`);
      if (!curRenderingEngine) return;
      // 获取视口
      const viewportId = `STACK_${renderIndex}`;
      let viewport;
      try {
        viewport = curRenderingEngine.getViewport(viewportId);
        // console.error(`视口未找到或未启用: ${viewportId}`);
        if (!viewport || !viewport.element) return;
      } catch (error) {
        console.error(`获取视口失败: ${viewportId}`, error);
        return;
      }

      // 获取或创建工具组
      const toolGroupId = `toolGroup-${seriesId}`;
      let toolGroup = ToolGroupManager.getToolGroup(toolGroupId);

      if (!toolGroup) {
        console.warn(`工具组不存在，尝试创建: ${toolGroupId}`);
        try {
          toolGroup = ToolGroupManager.createToolGroup(toolGroupId);
          // console.error(`创建工具组失败: ${toolGroupId}`);
          if (!toolGroup) return;
          // 初始化工具组
          initializeGlobalTools();
        } catch (error) {
          console.error(`创建工具组失败: ${toolGroupId}`, error);
          return;
        }
      }

      console.log(`使用工具组: ${toolGroupId}`);

      // 确保视口绑定到工具组
      const viewports = toolGroup.getViewportsInfo();
      const isViewportBound = viewports.some(
        (vp) => vp.viewportId === viewportId && vp.renderingEngineId === curRenderingEngine.id
      );

      if (!isViewportBound) {
        console.warn(`视口未绑定到工具组，尝试绑定: ${viewportId}`);
        try {
          // 先尝试移除可能存在的旧绑定
          try {
            toolGroup.removeViewports(curRenderingEngine.id, viewportId);
          } catch (e) {
            // 忽略错误，可能本来就没有绑定
          }

          // 添加视口到工具组
          toolGroup.addViewport(viewportId, curRenderingEngine.id);
          console.log(`视口绑定成功: ${viewportId}`);
        } catch (error) {
          console.error(`绑定视口失败: ${viewportId}`, error);
          return;
        }
      }

      // 获取实际工具名称
      const actualToolName = getActualToolName(currentTool);

      // 确保工具存在
      if (actualToolName !== 'Text' && !toolGroup.hasTool(actualToolName)) {
        console.warn(`工具不存在: ${actualToolName}，尝试添加...`);

        // 尝试添加缺失的工具
        try {
          // 使用工具映射表获取工具类
          const toolClassMap: Record<string, any> = {
            Length: cornerstoneTools.LengthTool,
            Angle: cornerstoneTools.AngleTool,
            Probe: cornerstoneTools.ProbeTool,
            RectangleROI: cornerstoneTools.RectangleROITool,
            EllipticalROI: cornerstoneTools.EllipticalROITool,
            PlanarFreehandROI: cornerstoneTools.PlanarFreehandROITool,
            ArrowAnnotate: cornerstoneTools.ArrowAnnotateTool,
            Crosshairs: cornerstoneTools.CrosshairsTool,
            Pan: cornerstoneTools.PanTool,
            Zoom: cornerstoneTools.ZoomTool,
            WindowLevel: cornerstoneTools.WindowLevelTool,
          };

          const toolClass = toolClassMap[currentTool];
          if (toolClass) {
            // 先确保全局注册
            try {
              cornerstoneTools.addTool(toolClass);
            } catch (e) {
              // 可能已经注册过了，忽略错误
            }

            // 添加到工具组
            toolGroup.addTool(toolClass);
            console.log(`成功添加工具: ${actualToolName} (${toolClass.toolName})`);
          } else {
            console.error(`未知工具: ${currentTool}`);
            return;
          }
        } catch (error) {
          console.error(`添加工具失败: ${actualToolName}`, error);
          return;
        }
      }

      // 获取 MouseBindings 枚举
      const { MouseBindings } = cornerstoneTools.Enums;

      // 重置所有工具状态
      try {
        console.log('重置所有工具状态...');

        // 先将所有工具设为被动状态
        ['Length', 'Angle', 'Probe', 'RectangleROI', 'CircleROI', 'EllipticalROI', 'PlanarFreehandROI', 'ArrowAnnotate'].forEach(
          (name) => {
            const toolName = getActualToolName(name);
            if (toolGroup.hasTool(toolName)) {
              toolGroup.setToolPassive(toolName);
            }
          }
        );

        // 只有在明确指定其他工具时才禁用十字线工具
        // 如果currentTool为空或者为'Crosshairs'，保持十字线工具激活
        if (toolGroup.hasTool(CrosshairsTool.toolName) && currentTool && currentTool !== 'Crosshairs') {
          toolGroup.setToolDisabled(CrosshairsTool.toolName);
          console.log(`禁用十字线工具，当前工具: ${currentTool}`);
        } else {
          // 确保十字线工具激活
          if (toolGroup.hasTool(CrosshairsTool.toolName)) {
            toolGroup.setToolActive(CrosshairsTool.toolName, {
              bindings: [{ mouseButton: MouseBindings.Primary }],
            });
            console.log(`保持十字线工具激活状态`);
          }
        }

        console.log('已重置工具状态');
      } catch (error) {
        console.warn('重置工具状态时出错:', error);
      }

      // 设置工具为激活状态
      try {
        if (currentTool === 'Text') {
          console.log('激活文本工具');
          // Text工具激活时，禁用所有其他工具
          toolGroup.setToolDisabled(CrosshairsTool.toolName);

          // 将其他工具设置为被动
          [
            WindowLevelTool.toolName,
            PanTool.toolName,
            ZoomTool.toolName,
            LengthTool.toolName,
            AngleTool.toolName,
            ProbeTool.toolName,
            RectangleROITool.toolName,
            CircleROITool.toolName,
            EllipticalROITool.toolName,
            PlanarFreehandROITool.toolName,
            ArrowAnnotateTool.toolName,
          ].forEach((toolName) => {
            if (toolGroup.hasTool(toolName)) {
              toolGroup.setToolPassive(toolName);
            }
          });

          // Text工具的特殊处理
          // 注意：这里我们不再使用setupTextTool和isTextToolActive
          // 因为这些函数可能在当前组件中不可用
          console.log('Text工具需要在toolSwitcher.ts中处理');
        } else if (currentTool === 'Crosshairs' || !currentTool) {
          console.log(`激活十字线工具 (当前工具: ${currentTool || '默认'})`);

          // 确保十字线工具激活
          toolGroup.setToolActive(CrosshairsTool.toolName, {
            bindings: [{ mouseButton: MouseBindings.Primary }],
          });
        } else {
          console.log(`激活工具: ${currentTool} (${actualToolName})`);

          // 禁用十字线工具，激活指定工具
          if (toolGroup.hasTool(CrosshairsTool.toolName)) {
            toolGroup.setToolDisabled(CrosshairsTool.toolName);
          }

          toolGroup.setToolActive(actualToolName, {
            bindings: [{ mouseButton: MouseBindings.Primary }],
          });
        }

        // 验证工具是否真的被激活
        try {
          const activeToolName = toolGroup.getActivePrimaryMouseButtonTool();
          console.log(`当前激活的工具: ${activeToolName}`);
        } catch (verifyError) {
          console.warn('无法验证工具激活状态:', verifyError);
        }

        // 强制刷新视口
        setTimeout(() => {
          try {
            curRenderingEngine.render();
            console.log('视口已刷新');
          } catch (e) {
            console.warn('刷新视口失败:', e);
          }
        }, 100);

        console.log(`成功应用当前工具: ${currentTool} (${actualToolName})`);
      } catch (activateError) {
        console.error('激活工具失败:', activateError);

        // 尝试使用全局工具切换函数
        try {
          console.log('尝试使用全局工具切换函数...');
          changeTool('', currentTool);
        } catch (fallbackError) {
          console.error('全局工具切换也失败:', fallbackError);
        }
      }
    } catch (error) {
      console.error('应用当前工具失败:', error);
    }
  };

  // 完全清理工具组
  const clearToolGroup = () => {
    const toolGroupId = 'MAIN_TOOL_GROUP';
    const toolGroup = ToolGroupManager.getToolGroup(toolGroupId);

    if (toolGroup) {
      const viewports = toolGroup.getViewportsInfo();
      console.log(`清理工具组，移除 ${viewports.length} 个视口`);

      // 移除所有视口
      for (const viewport of viewports) {
        try {
          toolGroup.removeViewports(viewport.renderingEngineId, viewport.viewportId);
          console.log(`已移除视口: ${viewport.viewportId}`);
        } catch (error) {
          console.warn(`移除视口失败: ${viewport.viewportId}`, error);
        }
      }
    }
  };

  // 暴露清理函数到全局，方便调试
  React.useEffect(() => {
    (window as any).clearToolGroup = clearToolGroup;
    (window as any).debugToolGroup = () => {
      const toolGroup = ToolGroupManager.getToolGroup('MAIN_TOOL_GROUP');
      if (toolGroup) {
        const viewports = toolGroup.getViewportsInfo();
        console.log('工具组调试信息:', {
          工具组ID: toolGroup.id,
          视口数量: viewports.length,
          视口详情: viewports,
        });
      } else {
        console.log('工具组不存在');
      }
    };

    return () => {
      delete (window as any).clearToolGroup;
      delete (window as any).debugToolGroup;
    };
  }, []);

  const setUpToolGroups = async () => {
    if (!rendererRef.current || !imageIds || !imageIds.length) return;

    console.log('设置工具组开始...');

    try {
      // 创建序列专用的工具组
      const toolGroupId = `toolGroup-${seriesId}`;
      let toolGroup = ToolGroupManager.getToolGroup(toolGroupId);

      if (!toolGroup) {
        console.log(`创建新工具组: ${toolGroupId}`);
        toolGroup = ToolGroupManager.createToolGroup(toolGroupId);
        if (toolGroup) {
          // 初始化工具组
          await initializeToolGroup(toolGroup);
          console.log(`创建序列专用工具组: ${toolGroupId}`);
        } else {
          console.error(`无法创建工具组: ${toolGroupId}`);
          return;
        }
      } else {
        console.log(`使用现有工具组: ${toolGroupId}`);
      }

      // 获取渲染引擎
      const curRenderingEngine = getRenderingEngine(String(seriesId));
      if (!curRenderingEngine) {
        console.error(`渲染引擎未找到: ${seriesId}`);
        return;
      }

      // 确保视口存在并已启用
      const viewportId = `STACK_${renderIndex}`;
      let viewport;

      try {
        viewport = curRenderingEngine.getViewport(viewportId);
        if (!viewport) {
          console.error(`视口未找到: ${viewportId}`);
          return;
        }

        if (!viewport.element) {
          console.error(`视口元素未找到: ${viewportId}`);
          return;
        }

        console.log(`视口已验证: ${viewportId}`);
      } catch (error) {
        console.error(`获取视口失败: ${viewportId}`, error);
        return;
      }

      // 检查视口是否已绑定到工具组
      const viewports = toolGroup.getViewportsInfo();
      const isViewportBound = viewports.some(
        (vp) => vp.viewportId === viewportId && vp.renderingEngineId === curRenderingEngine.id
      );

      if (!isViewportBound) {
        console.log(`添加视口到工具组: ${viewportId} -> ${toolGroupId}`);
        try {
          // 先尝试移除可能存在的旧绑定
          try {
            toolGroup.removeViewports(curRenderingEngine.id, viewportId);
          } catch (e) {
            // 忽略错误，可能本来就没有绑定
          }

          // 添加视口到工具组
          toolGroup.addViewport(viewportId, curRenderingEngine.id);
          console.log(`视口添加成功: ${viewportId}`);
        } catch (error) {
          console.error(`添加视口到工具组失败: ${viewportId}`, error);
          return;
        }
      } else {
        console.log(`视口已绑定到工具组: ${viewportId}`);
      }

      // 应用当前工具
      setTimeout(() => {
        // 如果没有明确的currentTool或者currentTool是Crosshairs，确保十字线工具激活
        if (!currentTool || currentTool === 'Crosshairs') {
          console.log(`设置工具组后强制激活十字线工具 (currentTool: ${currentTool || '未设置'})`);

          // 直接激活十字线工具，不通过reApplyCurrentTool
          try {
            if (toolGroup.hasTool(CrosshairsTool.toolName)) {
              toolGroup.setToolActive(CrosshairsTool.toolName, {
                bindings: [{ mouseButton: MouseBindings.Primary }],
              });
              console.log('✅ 十字线工具已强制激活');

              // 如果currentTool未设置，更新状态
              if (!currentTool) {
                dispatch({
                  type: 'views/save',
                  payload: { currentTool: 'Crosshairs' },
                });
                console.log('✅ 已更新currentTool状态为Crosshairs');
              }
            }
          } catch (error) {
            console.error('强制激活十字线工具失败:', error);
          }
        } else {
          // 如果有其他工具需要激活，调用通用函数
          reApplyCurrentTool();
        }
      }, 100);

      console.log('设置工具组完成');
    } catch (error) {
      console.error('设置工具组失败:', error);
    }
  };

  // 修改初始化工具组函数，确保使用全局注册的工具
  const initializeToolGroup = async (toolGroup: any) => {
    try {
      console.log('开始初始化工具组...');

      // 确保全局工具已注册
      if (!toolsRegistered) {
        initializeGlobalTools();
      }

      // 强制使用手动方法，确保工具正确添加
      console.log('强制使用手动方法添加工具，跳过 addManipulationBindings');

      // 如果上面的方法失败，回退到手动添加工具
      console.log('使用手动方法添加工具...');
      const { MouseBindings } = cornerstoneTools.Enums;

      // 使用工具类添加工具，确保正确的工具初始化
      const toolsToAdd = [
        { name: 'Pan', class: cornerstoneTools.PanTool },
        { name: 'Zoom', class: cornerstoneTools.ZoomTool },
        { name: 'Crosshairs', class: cornerstoneTools.CrosshairsTool },
        { name: 'WindowLevel', class: cornerstoneTools.WindowLevelTool },
        { name: 'StackScrollMouseWheel', class: cornerstoneTools.StackScrollMouseWheelTool },
        { name: 'StackScroll', class: cornerstoneTools.StackScrollTool },
        { name: 'Length', class: cornerstoneTools.LengthTool },
        { name: 'Angle', class: cornerstoneTools.AngleTool },
        { name: 'Probe', class: cornerstoneTools.ProbeTool },
        { name: 'RectangleROI', class: cornerstoneTools.RectangleROITool },
        { name: 'CircleROI', class: cornerstoneTools.CircleROITool },
        { name: 'EllipticalROI', class: cornerstoneTools.EllipticalROITool },
        { name: 'PlanarFreehandROI', class: cornerstoneTools.PlanarFreehandROITool },
        { name: 'ArrowAnnotate', class: cornerstoneTools.ArrowAnnotateTool },
      ];

      // 添加工具到工具组 - 强制重新添加，确保工具正确注册
      toolsToAdd.forEach(({ name, class: ToolClass }) => {
        try {
          // 强制添加工具，即使已经存在也重新添加
          toolGroup.addTool(ToolClass);
          console.log(`强制添加工具到工具组: ${name} (${ToolClass.toolName})`);

          // 验证工具是否真的被添加了
          if (toolGroup.hasTool(ToolClass.toolName)) {
            console.log(`✓ 验证成功: 工具 ${name} 已在工具组中`);
          } else {
            console.error(`✗ 验证失败: 工具 ${name} 未在工具组中找到`);
          }
        } catch (error) {
          console.error(`添加工具到工具组失败: ${name} (${ToolClass.toolName})`, error);

          // 尝试先全局注册工具，再添加到工具组
          try {
            console.log(`尝试先全局注册工具: ${name}`);
            cornerstoneTools.addTool(ToolClass);
            toolGroup.addTool(ToolClass);
            console.log(`重试成功: ${name}`);
          } catch (retryError) {
            console.error(`重试也失败: ${name}`, retryError);
          }
        }
      });

      // 设置默认激活工具 - 使用工具的实际toolName
      try {
        toolGroup.setToolActive(CrosshairsTool.toolName, {
          bindings: [{ mouseButton: MouseBindings.Primary }],
        });

        toolGroup.setToolActive(ZoomTool.toolName, {
          bindings: [{ mouseButton: MouseBindings.Secondary }],
        });

        toolGroup.setToolActive(StackScrollMouseWheelTool.toolName);

        toolGroup.setToolActive(WindowLevelTool.toolName, {
          bindings: [{ mouseButton: MouseBindings.Auxiliary }],
        });

        // 设置被动工具 - 使用工具的实际toolName
        toolGroup.setToolPassive(LengthTool.toolName);
        toolGroup.setToolPassive(AngleTool.toolName);
        toolGroup.setToolPassive(ProbeTool.toolName);
        toolGroup.setToolPassive(RectangleROITool.toolName);
        toolGroup.setToolPassive(CircleROITool.toolName);
        toolGroup.setToolPassive(EllipticalROITool.toolName);
        toolGroup.setToolPassive(PlanarFreehandROITool.toolName);
        toolGroup.setToolPassive(ArrowAnnotateTool.toolName);

        console.log('工具组初始化成功 - 使用手动方法，已设置所有工具状态');
      } catch (error) {
        console.error('设置工具状态失败:', error);
      }
    } catch (error) {
      console.error('工具组初始化失败:', error);
    }
  };

  // 确保所有工具都已正确添加到工具组
  const ensureToolsAreAdded = async (toolGroup: any) => {
    const requiredTools = [
      CrosshairsTool.toolName,
      LengthTool.toolName,
      AngleTool.toolName,
      ProbeTool.toolName,
      RectangleROITool.toolName,
      CircleROITool.toolName,
      EllipticalROITool.toolName,
      PlanarFreehandROITool.toolName,
      ArrowAnnotateTool.toolName,
    ];

    for (const toolName of requiredTools) {
      if (!toolGroup.hasTool(toolName)) {
        console.warn(`工具 ${toolName} 未添加到工具组，尝试重新添加`);
        try {
          toolGroup.addTool(toolName);
          console.log(`成功添加工具: ${toolName}`);
        } catch (error) {
          console.error(`添加工具失败: ${toolName}`, error);
        }
      }
    }
  };

  // 在视口设置后正确应用工具
  const applyToolAfterViewportSetup = (toolName: string, toolGroup: any, renderingEngine: any) => {
    try {
      // 检查参数是否有效
      if (!toolGroup || !renderingEngine) {
        console.error('工具应用失败: 工具组或渲染引擎未初始化');
        return;
      }

      console.log(`开始应用工具: ${toolName}`);

      // 显示工具组状态
      const viewports = toolGroup.getViewportsInfo();
      console.log(`工具组状态:`, {
        工具组ID: toolGroup.id,
        绑定的视口数量: viewports.length,
        视口详情: viewports.map((vp: any) => ({
          viewportId: vp.viewportId,
          renderingEngineId: vp.renderingEngineId,
        })),
      });

      // 验证工具组中是否包含所需的工具
      const requiredTools = [
        CrosshairsTool.toolName,
        LengthTool.toolName,
        AngleTool.toolName,
        ProbeTool.toolName,
        RectangleROITool.toolName,
        CircleROITool.toolName,
        EllipticalROITool.toolName,
        PlanarFreehandROITool.toolName,
        ArrowAnnotateTool.toolName,
      ];

      // 确保所有工具都在工具组中
      let missingTools = [];
      for (const tool of requiredTools) {
        try {
          if (!toolGroup.hasTool(tool)) {
            missingTools.push(tool);
            console.warn(`工具 ${tool} 不在工具组中`);
          }
        } catch (error) {
          console.error(`检查工具 ${tool} 时出错:`, error);
          missingTools.push(tool);
        }
      }

      if (missingTools.length > 0) {
        console.warn(`以下工具未在工具组中找到: ${missingTools.join(', ')}`);
      }

      // 重置所有工具状态
      try {
        toolGroup.setToolDisabled(CrosshairsTool.toolName);
        toolGroup.setToolPassive(LengthTool.toolName);
        toolGroup.setToolPassive(AngleTool.toolName);
        toolGroup.setToolPassive(ProbeTool.toolName);
        toolGroup.setToolPassive(RectangleROITool.toolName);
        toolGroup.setToolPassive(CircleROITool.toolName);
        toolGroup.setToolPassive(EllipticalROITool.toolName);
        toolGroup.setToolPassive(PlanarFreehandROITool.toolName);
        toolGroup.setToolPassive(ArrowAnnotateTool.toolName);

        console.log(`工具状态重置完成`);
      } catch (error) {
        console.warn(`重置工具状态时出错:`, error);
      }

      // 直接激活工具，不使用changeTool函数避免多工具组冲突
      try {
        if (toolName === 'Crosshairs') {
          console.log(`直接激活十字线工具`);
          toolGroup.setToolActive(CrosshairsTool.toolName, {
            bindings: [
              {
                mouseButton: MouseBindings.Primary,
              },
            ],
          });
        } else if (toolName === 'Pan') {
          // 禁用十字线工具，激活平移工具
          console.log(`激活平移工具`);
          toolGroup.setToolDisabled(CrosshairsTool.toolName);
          toolGroup.setToolActive(PanTool.toolName, {
            bindings: [
              {
                mouseButton: MouseBindings.Primary,
              },
            ],
            configuration: {
              mouseCursor: '/icon/leftToolBar/brain.png',
            },
          });
        } else {
          // 对于其他工具，禁用十字线并激活新工具
          console.log(`激活工具: ${toolName}`);
          toolGroup.setToolDisabled(CrosshairsTool.toolName);

          const actualToolName = getActualToolName(toolName);
          if (actualToolName && toolGroup.hasTool(actualToolName)) {
            toolGroup.setToolActive(actualToolName, {
              bindings: [
                {
                  mouseButton: MouseBindings.Primary,
                },
              ],
            });
          } else {
            console.warn(`工具 ${toolName}(${actualToolName}) 不可用或未在工具组中找到`);
          }
        }
      } catch (toolActivationError) {
        console.error(`激活工具 ${toolName} 失败:`, toolActivationError);
      }

      // 强制刷新渲染引擎，确保工具状态正确应用
      setTimeout(() => {
        try {
          renderingEngine?.render();
          console.log(`工具应用完成并刷新渲染引擎: ${toolName}`);
        } catch (error) {
          console.warn(`刷新渲染引擎时出错:`, error);
        }
      }, 100);
    } catch (error) {
      console.error(`应用工具失败: ${toolName}`, error);
      // 如果出错，尝试应用默认工具
      setTimeout(() => {
        try {
          console.log(`尝试应用默认十字线工具作为备选`);
          if (toolGroup && toolGroup.hasTool && toolGroup.hasTool(CrosshairsTool.toolName)) {
            toolGroup.setToolActive(CrosshairsTool.toolName, {
              bindings: [
                {
                  mouseButton: MouseBindings.Primary,
                },
              ],
            });
          }
          renderingEngine?.render();
          console.log(`应用默认工具成功`);
        } catch (fallbackError) {
          console.error(`应用默认工具也失败:`, fallbackError);
        }
      }, 200);
    }
  };

  const togglePlayPause = () => {
    if (!rendererRef.current) return;

    const element = rendererRef.current;

    if (isPlaying) {
      csToolsUtilities.cine.stopClip(element);
    } else {
      csToolsUtilities.cine.playClip(element, { framesPerSecond });
    }

    setIsPlaying(!isPlaying);
  };

  // 获取当前序列的DICOM标签数据
  const getCurrentImageData = useMemo(() => {
    // console.log('getCurrentImageData计算中, InstanceTag:', InstanceTag);
    // console.log('InstanceTag类型:', typeof InstanceTag);
    // console.log('InstanceTag是数组吗:', Array.isArray(InstanceTag));

    if (!InstanceTag) return null;

    // 如果InstanceTag是数组，取第一个元素
    if (Array.isArray(InstanceTag)) {
      // console.log('InstanceTag是数组，长度:', InstanceTag.length);
      return InstanceTag.length > 0 ? InstanceTag[0] : null;
    }

    // 如果InstanceTag是对象，直接返回
    if (typeof InstanceTag === 'object') {
      // console.log('InstanceTag是对象，直接返回');
      return InstanceTag;
    }

    // console.log('InstanceTag格式不正确');
    return null;
  }, [InstanceTag]);

  // 获取当前图像的DICOM标签信息
  const instanceInfo = useMemo(() => {
    if (curInstances && seriesId) {
      const instances = curInstances.get(seriesId);
      if (instances && instances.length > 0) {
        return instances[0]; // 使用序列的第一个实例作为代表
      }
    }
    return null;
  }, [curInstances, seriesId]);

  // 判断图像类型
  const getImageType = (): 'ct' | 'pet' | 'fusion' => {
    if (!getCurrentImageData) return 'ct';

    // 从DICOM标签中获取Modality信息
    const modalityTag = getCurrentImageData['(0008,0060)'];
    const modality = modalityTag && modalityTag.Value && modalityTag.Value[0] ? String(modalityTag.Value[0]) : '';

    // console.log('图像类型判断 - modality:', modality);

    if (modality === 'PT') return 'pet';
    if (modality === 'CT') return 'ct';
    // 这里可以根据实际需求判断融合图像
    return 'ct';
  };
  // 监听 MIP 相关属性变化
  useEffect(() => {
    if (!rendererRef.current || !imageIds || !imageIds.length) return;
    console.log('orientation调试 - 监听MIP属性变化', orientation);
    try {
      const curRenderingEngine = getRenderingEngine(String(seriesId));
      if (!curRenderingEngine) {
        console.log('渲染引擎未找到');
        return;
      }

      const viewportId = `STACK_${renderIndex}`;
      const viewport = curRenderingEngine.getViewport(viewportId) as IVolumeViewport;

      if (!viewport) {
        console.log('视口未找到');
        return;
      }

      console.log('MIP 参数变化:', {
        mipEnabled,
        mipType,
        axialValue,
        coronalValue,
        sagittalValue,
        orientation,
      });

      // 应用 MIP 设置
      if (mipEnabled && mipType) {
        // 根据当前视图方向确定应该使用哪个厚度值
        let slabThickness = 1; // 默认厚度为1（相当于单层切片）

        // 根据视图方向选择对应的投影厚度
        if (orientation === 'axial' && axialValue !== undefined) {
          // 轴状面（水平面）使用 axialValue 作为厚度
          slabThickness = axialValue;
        } else if (orientation === 'sagittal' && sagittalValue !== undefined) {
          // 矢状面（左右面）使用 sagittalValue 作为厚度
          slabThickness = sagittalValue;
        } else if (orientation === 'coronal' && coronalValue !== undefined) {
          // 冠状面（前后面）使用 coronalValue 作为厚度
          slabThickness = coronalValue;
        }

        // 确保厚度至少为1，避免出现0厚度导致图像消失
        slabThickness = Math.max(1, slabThickness);

        // 创建属性对象
        const properties = {};

        // 设置厚度
        (properties as any).slabThickness = slabThickness;

        // 根据 MIP 类型设置投影模式
        if (mipType === 'MinIP') {
          (properties as any).blendMode = 'MIN_IP';
        } else if (mipType === 'MIP') {
          (properties as any).blendMode = 'MIP';
        } else if (mipType === 'Mean') {
          (properties as any).blendMode = 'AVERAGE';
        } else if (mipType === 'Sum') {
          (properties as any).blendMode = 'SUM';
        }

        console.log('应用 MIP 设置:', {
          orientation,
          slabThickness,
          blendMode: (properties as any).blendMode,
        });

        // 应用属性
        viewport.setProperties(properties);
      } else {
        // 禁用 MIP，恢复正常渲染（单层切片）
        const properties = {};
        (properties as any).slabThickness = 1;
        (properties as any).blendMode = 'COMPOSITE';

        viewport.setProperties(properties);

        console.log('禁用 MIP 设置，恢复单层切片');
      }

      // 重新渲染视图
      viewport.render();
    } catch (error) {
      console.error('应用 MIP 设置失败:', error);
    }
  }, [mipEnabled, mipType, axialValue, coronalValue, sagittalValue, imageIds, orientation]);

  useEffect(() => {
    // 确保 Cornerstone 和工具初始化
    initCornerstone().then(() => {
      console.log('Cornerstone 初始化完成，开始注册工具...');
      // 初始化全局工具
      initializeGlobalTools();

      // 暴露调试函数
      (window as any).debugTools = () => {
        console.log('工具注册状态:', toolsRegistered);
        console.log('当前工具:', currentTool);

        // 检查工具组
        const toolGroups: Array<{
          id: string;
          viewports: any[];
          tools: string[];
        }> = [];

        try {
          // 尝试获取所有工具组
          const toolGroupIds = [`toolGroup-${seriesId}`, 'MAIN_TOOL_GROUP'];
          toolGroupIds.forEach((id) => {
            try {
              const toolGroup = ToolGroupManager.getToolGroup(id);
              if (toolGroup) {
                // 获取工具名称的安全方法
                let toolNames: string[] = [];
                try {
                  // 使用备用方法：手动检查常见工具
                  const commonTools = [
                    'Pan',
                    'Zoom',
                    'Crosshairs',
                    'WindowLevel',
                    'StackScrollMouseWheel',
                    'Length',
                    'Angle',
                    'Probe',
                    'RectangleROI',
                    'EllipticalROI',
                    'PlanarFreehandROI',
                    'ArrowAnnotate',
                  ];
                  toolNames = commonTools.filter((name) => toolGroup.hasTool(name));
                } catch (e) {
                  console.warn('获取工具名称失败:', e);
                }

                toolGroups.push({
                  id: toolGroup.id,
                  viewports: toolGroup.getViewportsInfo(),
                  tools: toolNames,
                });
              }
            } catch (e) {
              console.warn(`获取工具组 ${id} 失败:`, e);
            }
          });
        } catch (e) {
          console.error('获取工具组失败:', e);
        }

        console.log('工具组信息:', toolGroups);
      };
    });

    return () => {
      delete (window as any).debugTools;
    };
  }, []);

  // 仅在Probe工具激活时，监听鼠标事件
  useEffect(() => {
    const container = rendererRef.current;
    if (!container) return;
    if (currentTool !== 'Probe') {
      setShowProbe(false);
      return;
    }

    // 鼠标按下
    const handleMouseDown = (event: MouseEvent) => {
      console.log('Probe mousedown triggered', event.button);
      if (event.button !== 0) return; // 只处理左键
      mouseDownRef.current = true;
      setShowProbe(true);
      handleProbe(event);
      console.log('Probe mousedown state set');
    };
    // 鼠标移动
    const handleMouseMove = (event: MouseEvent) => {
      if (!mouseDownRef.current) return;
      handleProbe(event);
    };
    // 鼠标松开
    const handleMouseUp = (event: MouseEvent) => {
      console.log('Probe mouseup triggered', event.button);
      if (event.button !== 0) return;
      mouseDownRef.current = false;
      setShowProbe(false);
      setPixelValue('0'); // 清除像素值
      console.log('Probe state cleared');
    };
    // 点测量逻辑
    const handleProbe = (event: MouseEvent) => {
      const rect = container.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;
      setMousePosition({ x, y });
      // 复用原有像素值获取逻辑
      // ...调用原有像素值获取代码，设置setPixelValue(...)
      // 这里直接调用handleMouseMove的核心部分
      // 获取当前视口
      const viewportId = `STACK_${renderIndex}`;
      const curRenderingEngine = getRenderingEngine(String(seriesId));
      if (!curRenderingEngine) return;
      const viewport = curRenderingEngine.getViewport(viewportId) as IVolumeViewport;
      if (!viewport) return;
      try {
        const canvasPos = [x, y] as [number, number];
        const worldPos = viewport.canvasToWorld(canvasPos);
        // 3D体积
        const volumes = (viewport as any).getVolumes?.();
        if (volumes && volumes.length > 0) {
          const volume = volumes[0];
          if (volume && volume.imageData) {
            const { dimensions, imageData } = volume;
            const index = imageData.worldToIndex(worldPos);
            index[0] = Math.floor(index[0]);
            index[1] = Math.floor(index[1]);
            index[2] = Math.floor(index[2]);
            if (!csUtils.indexWithinDimensions(index, dimensions)) {
              setPixelValue('N/A');
              return;
            }
            if (volume.voxelManager) {
              const value = volume.voxelManager.getAtIJK(index[0], index[1], index[2]);
              if (value !== undefined && value !== null) {
                setPixelValue(value.toFixed(3));
                return;
              }
            }
          }
        } else {
          // 2D图像
          const imageData = (viewport as any).getImageData?.();
          if (imageData) {
            let scalarData = null;
            if (imageData.getScalarData) {
              scalarData = imageData.getScalarData();
            }
            if (!scalarData && imageData.scalarData) {
              scalarData = imageData.scalarData;
            }
            if (scalarData) {
              const canvas = (viewport as any).getCanvas?.();
              if (canvas) {
                const rect = canvas.getBoundingClientRect();
                const canvasX = Math.floor((x / rect.width) * canvas.width);
                const canvasY = Math.floor((y / rect.height) * canvas.height);
                if (canvasX >= 0 && canvasX < canvas.width && canvasY >= 0 && canvasY < canvas.height) {
                  const index = canvasY * canvas.width + canvasX;
                  if (index < scalarData.length) {
                    const value = scalarData[index];
                    if (value !== undefined) {
                      setPixelValue(value.toFixed(3));
                      return;
                    }
                  }
                }
              }
            }
          }
        }
        setPixelValue('N/A');
      } catch (error) {
        setPixelValue('N/A');
      }
    };
    container.addEventListener('mousedown', handleMouseDown);
    container.addEventListener('mousemove', handleMouseMove);
    container.addEventListener('mouseup', handleMouseUp);
    return () => {
      container.removeEventListener('mousedown', handleMouseDown);
      container.removeEventListener('mousemove', handleMouseMove);
      container.removeEventListener('mouseup', handleMouseUp);
    };
  }, [currentTool, rendererRef, renderIndex, seriesId]);

  return seriesId ? (
    <div
      ref={rendererRef}
      className={styles.dicomContainer}
      style={{
        width: '100%',
        height: '100%',
        position: 'relative',
        padding: '1px', // 最小化内边距以最大化可用空间
        boxSizing: 'border-box',
        overflow: 'hidden', // 防止内容溢出
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      {/* 视图方向切换控件 */}
      {newSeriesLists && newSeriesLists.length > 0 && (
        <ViewControls
          orientation={orientation}
          onOrientationChange={(newOrientation) => {
            if (onOrientationChange) onOrientationChange(newOrientation);
          }}
        />
      )}

      {/* 序列切换控件 - 在每个视图中显示 */}
      {newSeriesLists && newSeriesLists.length > 0 && (
        <SeriesControls
          seriesId={seriesId}
          seriesLists={newSeriesLists}
          onSeriesChange={(newSeriesId) => {
            console.log('Dicom2D: 切换序列', newSeriesId);
            if (onSeriesChange) {
              onSeriesChange(newSeriesId);
            }
          }}
        />
      )}
      {cornerInfoConfig && (
        <CornerInfoOverlay
          config={cornerInfoConfig}
          imageData={currentInstanceInfo}
          imageType={getImageType()}
          sensitiveInfoHidden={sensitiveInfoHidden}
          mousePosition={mousePosition}
          pixelValue={pixelValue}
          showProbe={showProbe}
          windowWidth={windowWidth}
          windowCenter={windowCenter}
          index={currentIndex}
        />
      )}

      {/* 方向指示器 */}
      {directionIndicatorVisible && <DirectionIndicator />}

      {/* 播放控制 */}
      <div className={styles.playControls}>
        <Button onClick={togglePlayPause} type={isPlaying ? 'dashed' : 'primary'}>
          {isPlaying ? <PauseOutlined /> : <CaretRightOutlined />}
          &nbsp;{isPlaying ? '暂停' : '播放'}
        </Button>

        <span style={{ marginLeft: 16, color: '#fff', marginRight: 8 }}>FPS:</span>
        <Select
          value={framesPerSecond}
          onChange={(value) => {
            setFramesPerSecond(value);
            if (isPlaying && rendererRef.current) {
              csToolsUtilities.cine.stopClip(rendererRef.current);
              csToolsUtilities.cine.playClip(rendererRef.current, { framesPerSecond: value });
            }
          }}
          className={styles.cineSelect}
        >
          <Option value={60}>60</Option>
          <Option value={30}>30</Option>
          <Option value={15}>15</Option>
        </Select>
      </div>
    </div>
  ) : null;
});

const mapStateToProps = ({ prints, views }: { prints: PrintType; views: ViewType }) => {
  return {
    views, // 传递整个 views 状态
    mouseLeftTool: views.isFusionMode ? IMouseLeftToolType.INVERT : IMouseLeftToolType.DEFAULT,
    seriesLists: views.seriesLists,
    instanceLists: views.instanceLists,
    curInstances: views.curInstances,
    InstanceTag: views.InstanceTag,
    resetViewport: views.resetViewport,
    windowWidth: views.windowWidth,
    windowCenter: views.windowCenter,
    annotationDeletedTrigger: views.annotationDeletedTrigger,
    selectedColorMap: views.selectedColorMap,
    cornerInfoConfig: views.cornerInfoConfig,
    directionIndicatorVisible: views.directionIndicatorVisible,
    sensitiveInfoHidden: views.sensitiveInfoHidden,
    currentTool: views.currentTool,
    // MIP相关属性
    mipEnabled: views.mipEnabled,
    mipType: views.mipType,
    axialValue: views.axialValue,
    coronalValue: views.coronalValue,
    sagittalValue: views.sagittalValue,
    curSeriesId: views.curSeriesId,
  };
};

export default connect(mapStateToProps)(Dicom2D);
