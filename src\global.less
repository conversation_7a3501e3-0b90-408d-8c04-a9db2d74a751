// 全局样式文件
// https://umijs.org/zh-CN/docs/assets-css

// @import '~antd/lib/style/themes/default.less';
// @import '~antd/dist/antd.less'; // 引入官方提供的 less 样式入口文件

@primary-color: #39bbdb; // 全局主色

// 引入外部样式
//@import '~rayplus-three-view/dist/esm/style.css';

// .ant-select-selector {
//   background-color: #000 !important;
//   color: #fff !important;
// }

// .ant-select-item {
//   color: #fff !important;
//   background-color: rgba(38, 38, 40, 1) !important;
//   border: none !important;
//   box-shadow: none !important;
//   outline: none !important;

//   &:hover,
//   &:active,
//   &:focus {
//     background-color: #555 !important;
//     color: #fff !important;
//     border: none !important;
//     box-shadow: none !important;
//   }
//   border: none !important;
// }

// global.less
// :global {
//   .ant-select {
//     .ant-select-selector {
//       background-color: rgba(51, 50, 51, 1) !important;
//       color: #fff !important;
//       border: none !important;
//     }

//     .ant-select-item {
//       color: #fff !important;
//       background-color: rgba(38, 38, 40, 1) !important;

//       &:hover,
//       &:focus,
//       &:active {
//         background-color: #555 !important;
//         color: #fff !important;
//       }
//     }

//     .ant-select-item-selected {
//       background-color: rgba(132, 63, 255, 0.4) !important;
//       color: #fff !important;
//     }

//     .ant-select-arrow {
//       color: #fff !important;
//     }
//   }

//   .ant-select-focused .ant-select-selector,
//   .ant-select-selector:focus,
//   .ant-select-selector:active {
//     border-color: transparent !important;
//     box-shadow: 0 0 2px rgba(132, 63, 255, 1) !important;
//   }
// }

// 全局自定义滚动条样式
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb {
  background-image: url('/icon/scroll_bar.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  border-radius: 5px;
  border: none;
}

::-webkit-scrollbar-thumb:hover {
  opacity: 0.8;
}

::-webkit-scrollbar-thumb:active {
  opacity: 0.6;
}

::-webkit-scrollbar-corner {
  background: transparent;
}

// 针对Firefox的滚动条样式（限制支持）
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}


.ant-input-affix-wrapper {
  background-color: #333233!important;
  ::placeholder {
    color: #ffffff73!important;
  }
  .ant-input-suffix .ant-input-password-icon {
    color: #FFF;
  }
}

// 针对Antd message提示字体颜色做全局覆盖
.ant-message-notice-content {
  color: #333 !important;
  background-color: #ffffff !important;
}

// 针对Modal确认对话框样式做全局覆盖
.ant-modal-confirm .ant-modal-content {
  background-color: #333333 !important;
}

.ant-modal-confirm .ant-modal-content .ant-modal-body {
  background-color: #333333 !important;
}

.ant-modal-confirm .ant-modal-content .ant-modal-title,
.ant-modal-confirm .ant-modal-content .ant-modal-confirm-title,
.ant-modal-confirm .ant-modal-content .ant-modal-confirm-content {
  color: #ffffff !important;
}

.ant-modal-confirm .ant-modal-content .ant-btn {
  background-color: #1f69b4 !important;
  border-color: #1f69b4 !important;
  color: #ffffff !important;
}

.ant-modal-confirm .ant-modal-content .ant-btn-default {
  background-color: #555555 !important;
  border-color: #555555 !important;
  color: #ffffff !important;
}
.ant-select .ant-select-arrow,.anticon-close-circle {
  color: #fff;
}