.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #191919;
}
.ToolBarContainer {
  margin: 0 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: center;
  background-color: #262628;
  padding: 10px 0;
  border-radius: 12px;
  //background-color: yellow;
}

.roiDropdown {
  width: 125%;
  position: relative;
  display: inline-block;
  text-align: center; /* 确保ROI图标居中 */
}

.dropdownContent {
  display: none;
  position: absolute;
  background-color: black;
  width: auto; /* 根据内容自适应宽度 */
  min-width: 50px;
  // box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  z-index: 1;
  flex-direction: column;
  padding: 8px 0;
  left: 50%; /* 从中心点开始 */
  transform: translateX(-50%); /* 向左偏移自身宽度的一半，实现居中 */
}

.ROIIcon {
  color: #ffffff;
  cursor: pointer;
}

.icon {
  width: 25%;
  margin-top: 4px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    filter: brightness(1.2);
  }
}

.activeIcon {
  // filter: brightness(1.5) drop-shadow(0 0 8px rgba(24, 144, 255, 0.8));
  background-color: #1F69B4;
  border-radius: 4px;
  padding: 4px;
}

.arrowIcon {
  width: 8%;
  margin-top: 4px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    filter: brightness(1.2);
  }
}

.colorIcon {
  width: 50%;
  margin-top: 4px;
}
.separator {
  width: 90%;
  height: 1px;
  background-color: white;
  margin: 5px 0;
}

.layoutGrid {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  justify-content: center;
  margin-bottom: 20px;
}
.layoutGridDiv {
  width: 90px;
  height: 90px;
  border: 1px solid #ccc;
  border-radius: 2px;
  box-sizing: border-box;
  background-color: white;
}
.numberContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
}

.leftModal {
  :global {
    /* 修改 Select 选择器区域样式 */
    .ant-select {
      .ant-select-selector {
        background-color: rgba(51, 50, 51, 1) !important;
        color: rgba(255, 255, 255, 1) !important;
        // border: none !important;
      }

      /* 修改下拉菜单的背景和文字颜色 */
      .ant-select-item {
        color: #fff !important;
        background-color: rgba(38, 38, 40, 1) !important;

        &:hover,
        &:active,
        &:focus {
          background-color: #555 !important;
          color: #fff !important;
        }
      }

      /* 修改下拉菜单选中项的样式 */
      .ant-select-item-option-selected {
        background-color: #1677ff !important;
        color: #fff !important;

        &:hover {
          background-color: #154dad !important;
        }
      }

      /* 可选：修改下拉箭头图标颜色 */
      .ant-select-arrow {
        color: #fff !important;
      }
    }

    .ant-modal {
      padding: 0 !important;
    }

    .ant-modal-content {
      background-color: #262626 !important;
      color: #fff !important;
      border-radius: 4px !important;
      overflow: hidden !important;
      box-shadow: none !important;
      padding: 0 !important;
      display: flex !important;
      flex-direction: column !important;
    }

    .ant-modal-header {
      background-color: rgba(76, 76, 76, 1) !important;
      border-bottom: none !important;
      padding: 10px 16px !important;
      margin: 0 !important;
      width: 100% !important;
      border-radius: 4px 4px 0 0 !important;
      position: relative !important;
    }

    .ant-modal-title {
      color: #fff !important;
      font-size: 16px !important;
      font-weight: normal !important;
    }

    .ant-modal-close {
      color: #fff !important;
      top: 0 !important;
      right: 0 !important;
      z-index: 10 !important;

      .ant-modal-close-x {
        width: 40px !important;
        height: 40px !important;
        line-height: 40px !important;
        font-size: 16px !important;
      }
    }

    .ant-modal-body {
      background-color: #262626 !important;
      padding: 16px !important;
      border-radius: 0 0 4px 4px !important;
      flex: 1 !important;
    }

    /* 所有按钮通用样式 */
    .ant-btn {
      color: #fff !important;

      &:hover,
      &:focus {
        color: #fff !important;
      }
    }

    /* 取消按钮（默认类型）样式 */
    .ant-btn-default {
      background-color: #262628 !important;
      border: none !important;

      &:hover,
      &:focus {
        background-color: #4d4d50 !important;
      }
    }

    /* 确认按钮（主类型）样式 */
    .ant-btn-primary {
      background-color: #1677ff !important; /* 蓝色背景 */
      border: none !important;

      &:hover,
      &:focus {
        background-color: #154dad !important; /* 深蓝 hover */
      }
    }
  }
}

:global {
  .fusion-modal-wrapper {
    .ant-modal {
      padding-bottom: 0 !important;
      top: 50% !important;
      padding-top: 0 !important;
      margin: 0 auto !important;
      transform: translateY(-50%) !important;
    }

    .ant-modal-wrap {
      overflow: hidden !important;
    }

    .ant-modal-mask {
      background-color: rgba(0, 0, 0, 0.6) !important;
    }

    .ant-modal-content {
      box-shadow: none !important;
    }
  }

  /* 全局下拉菜单样式修复 */
  .ant-select-dropdown {
    background-color: #262628 !important;
    border: 1px solid #555 !important;

    .ant-select-item {
      color: #fff !important;
      background-color: #262628 !important;

      &:hover {
        background-color: #555 !important;
        color: #fff !important;
      }

      &.ant-select-item-option-selected {
        background-color: #1677ff !important;
        color: #fff !important;

        &:hover {
          background-color: #154dad !important;
        }
      }
    }
  }
}

.GridInputNumber {
  :global {
    .ant-input-number-input {
      background-color: rgba(51, 50, 51, 1) !important;
      color: #fff !important;
      border: none !important;
    }
  }
}

.GridInputNumber,
.GlobalInputStyle {
  :global {
    .ant-input-number-input,
    .ant-input {
      background: transparent;
      color: #fff !important;
    }
    /* 分开写 placeholder */
    .ant-input-number-input::placeholder,
    .ant-input::placeholder {
      color: #333233;
    }
  }
}
.toolIcon {
  color: #ffffff;
}

.closeIcon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 28px;
  height: 28px;
  color: #ffffff;
  font-weight: 600;
  border-radius: 50%;
  background-color: #D92E2D;
}
// 弹出框 样式重新
.modalHeader {
  padding: 12px 20px!important;
  font-weight: 400!important;
  background-color: #4C4C4C!important;
}
.modalContent {
  color: #ffffff;
  padding: 0 0 24px!important;
  background-color: #333233!important;
}
.modalBody {
  padding:16px 24px 0!important;
}
.modalFooter {
  border-radius: 10px!important;
  margin: 0px 16px;
}