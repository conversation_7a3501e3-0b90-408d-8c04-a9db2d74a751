

.seriesSection {
  flex: 1;
  min-width: 300px;
  
  .sectionTitle {
    color: #fff;
    font-size: 16px;
    margin-bottom: 12px;
  }
  
  :global(.ant-table) {
    background-color: #2a2a2a;
    
    :global(.ant-table-thead) {
      :global(.ant-table-cell) {
        background-color: #333;
        color: #fff;
        border-bottom: 1px solid #444;
        font-weight: 600;
      }
    }
    
    :global(.ant-table-tbody) {
      :global(.ant-table-cell) {
        background-color: #2a2a2a;
        color: #ccc;
        border-bottom: 1px solid #444;
      }
    }
  }
  
  .tableRow {
    &:hover {
      background-color: #3a3a3a !important;
      
      :global(.ant-table-cell) {
        background-color: #3a3a3a !important;
      }
    }
  }
  
  .selectedRow {
    background-color: #1890ff !important;
    
    :global(.ant-table-cell) {
      background-color: #1890ff !important;
      color: #fff !important;
    }
    
    &:hover {
      background-color: #40a9ff !important;
      
      :global(.ant-table-cell) {
        background-color: #40a9ff !important;
      }
    }
  }
}

.tagsSection {
  flex: 2;
  min-width: 400px;
  
  .sectionTitle {
    color: #fff;
    font-size: 16px;
    margin-bottom: 12px;
  }
  
  .loadingContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
    color: #ccc;
    
    p {
      margin-top: 16px;
      color: #ccc;
    }
  }
  
  :global(.ant-table) {
    background-color: #2a2a2a;
    
    :global(.ant-table-thead) {
      :global(.ant-table-cell) {
        background-color: #333;
        color: #fff;
        border-bottom: 1px solid #444;
        font-weight: 600;
      }
    }
    
    :global(.ant-table-tbody) {
      :global(.ant-table-cell) {
        background-color: #2a2a2a;
        color: #ccc;
        border-bottom: 1px solid #444;
      }
      
      :global(.ant-table-row):hover {
        :global(.ant-table-cell) {
          background-color: #3a3a3a !important;
        }
      }
    }
  }
  
  :global(.ant-pagination) {
    :global(.ant-pagination-item) {
      background-color: #333;
      border-color: #444;
      
      a {
        color: #ccc;
      }
      
      &:hover {
        background-color: #444;
        border-color: #555;
        
        a {
          color: #fff;
        }
      }
    }
    
    :global(.ant-pagination-item-active) {
      background-color: #1890ff;
      border-color: #1890ff;
      
      a {
        color: #fff;
      }
    }
    
    :global(.ant-pagination-prev),
    :global(.ant-pagination-next) {
      background-color: #333;
      border-color: #444;
      color: #ccc;
      
      &:hover {
        background-color: #444;
        border-color: #555;
        color: #fff;
      }
    }
    
    :global(.ant-pagination-total-text) {
      color: #ccc;
    }
    
    :global(.ant-select) {
      :global(.ant-select-selector) {
        background-color: #333;
        border-color: #444;
        color: #ccc;
      }
    }
    
    :global(.ant-pagination-jump-prev),
    :global(.ant-pagination-jump-next) {
      color: #ccc;
      
      &:hover {
        color: #fff;
      }
    }
  }
}

.closeIcon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 28px;
  height: 28px;
  color: #ffffff;
  font-weight: 600;
  border-radius: 50%;
  background-color: #D92E2D;
}
// 弹出框 样式重新
.modalHeader {
  padding: 12px 20px!important;
  font-weight: 400!important;
  background-color: #4C4C4C!important;
}
.modalContent {
  color: #ffffff;
  padding: 0 0 24px!important;
  background-color: #333233!important;
}
.modalBody {
  padding:16px 24px 0!important;
}
.modalFooter {
  border-radius: 10px!important;
  margin: 0px 16px;
}