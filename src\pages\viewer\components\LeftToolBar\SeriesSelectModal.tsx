import React, { useState, useEffect } from 'react';
import { Row, Col, Button, Radio, Empty, message } from 'antd';
import { connect, type Dispatch } from 'umi';
import { ViewType } from '@/models/views';
import styles from './SeriesSelectModal.less';

interface SeriesSelectModalProps {
    dispatch: Dispatch;
    onOk?: () => void;
    onCancel?: () => void;
}

type Props = SeriesSelectModalProps & {
    seriesLists?: any[];
    selectedSeriesId?: string;
    curSeriesId?: string;
};

const SeriesSelectModal: React.FC<Props> = React.memo((props) => {
    const { dispatch, seriesLists, selectedSeriesId, curSeriesId, onOk, onCancel } = props;

    // 使用本地状态管理临时选择的序列ID，避免影响阅片页面
    const [tempSelectedSeriesId, setTempSelectedSeriesId] = useState<string | undefined>(undefined);

    // 初始化时设置当前选中的序列ID
    useEffect(() => {
        setTempSelectedSeriesId(curSeriesId || selectedSeriesId);
    }, [curSeriesId, selectedSeriesId]);

    // 处理确定按钮点击
    const handleOk = () => {
        if (tempSelectedSeriesId) {
            // 确定后才切换到选中的序列
            dispatch({
                type: 'views/save',
                payload: {
                    curSeriesId: tempSelectedSeriesId,
                    selectedSeriesId: tempSelectedSeriesId,
                },
            });

            // 获取选中序列的instances
            dispatch({
                type: 'views/fetchInstances',
                payload: tempSelectedSeriesId,
            });

            message.success('序列选择成功');
            onOk?.();
        }
    };

    // 处理序列选择 - 只更新临时选择状态，不影响当前显示的序列
    const handleSeriesSelect = (seriesId: string) => {
        setTempSelectedSeriesId(seriesId);
    };

    // 渲染序列列表
    const renderSeriesList = () => {
        if (!seriesLists || seriesLists.length === 0) {
            return <Empty description='暂无数据' image={Empty.PRESENTED_IMAGE_SIMPLE} />;
        }

        // 过滤掉injector序列，使其在序列选择时不可选择
        const filteredSeriesLists = seriesLists.filter(series => series.SeriesDescription !== 'injector');

        if (filteredSeriesLists.length === 0) {
            return <Empty description='暂无可选择的序列' image={Empty.PRESENTED_IMAGE_SIMPLE} />;
        }

        return (
            <div>
                <div className={styles.seriesListHeader}>
                    <div className={styles.radioWrapper}></div>
                    <div className={styles.seriesNumber}>序号</div>
                    <div className={styles.seriesDescription}>序列描述</div>
                </div>
                <div className={styles.seriesList}>
                    {filteredSeriesLists.map((series, index) => {
                        const isSelected = tempSelectedSeriesId === series.SeriesId;

                        return (
                            <div key={series.SeriesId} className={`${styles.seriesItem} ${isSelected ? styles.selected : ''}`}>
                                <div className={styles.radioWrapper}>
                                    <Radio
                                        checked={isSelected}
                                        onChange={() => {
                                            handleSeriesSelect(series.SeriesId);
                                        }}
                                    />
                                </div>
                                <div className={styles.seriesNumber}>
                                    {index + 1}
                                </div>
                                <div className={styles.seriesDescription}>
                                    {series.SeriesDescription || '无描述'}
                                </div>
                            </div>
                        );
                    })}
                </div>
            </div>
        );
    };

    return (
        <div className={styles.fusionModalContent}>
            <Row gutter={16} style={{ margin: 0 }}>
                <Col span={24} style={{ paddingLeft: 0, paddingRight: 0 }}>
                    <div style={{
                        backgroundColor: '#262628',
                        borderRadius: '8px',
                        padding: '16px',
                        marginBottom: '16px',
                        width: '1251.71px',
                        height: '642.5px'
                    }}>
                        <div className={styles.seriesListContainer}>{renderSeriesList()}</div>
                    </div>
                </Col>
            </Row>
            <div className={styles.modalFooter}>
                <Button onClick={onCancel}>取消</Button>
                <Button
                    type={!tempSelectedSeriesId ? undefined : 'primary'}
                    disabled={!tempSelectedSeriesId}
                    onClick={handleOk}
                    style={
                        !tempSelectedSeriesId
                            ? {
                                backgroundColor: 'rgba(0, 0, 0, 0.04)',
                                borderColor: 'rgb(126, 126, 127)',
                                color: 'rgb(126, 126, 127)',
                                textShadow: 'none',
                                boxShadow: 'none',
                                cursor: 'not-allowed',
                            }
                            : undefined
                    }
                >
                    确定
                </Button>
            </div>
        </div>
    );
});

const mapStateToProps = ({ views }: { views: ViewType }) => {
    return {
        seriesLists: views.seriesLists,
        selectedSeriesId: views.selectedSeriesId,
        curSeriesId: views.curSeriesId,
    };
};

export default connect(mapStateToProps)(SeriesSelectModal); 